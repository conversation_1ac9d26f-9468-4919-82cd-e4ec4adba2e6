/**
 * Enhanced code generation utilities for App Builder
 * Supports multiple frameworks, TypeScript, accessibility, and modern best practices
 */

import {
  generateVueCode,
  generateAngularCode,
  generateSvelteCode,
  generateNextJSCode,
  generateReactNativeCode
} from './enhanced_code_generators.js';

import {
  generatePackageJson,
  generateReadme,
  generateTSConfig,
  generateESLintConfig,
  generateDockerfile,
  generateDockerCompose,
  generateGitHubActions,
  generateViteConfig
} from './project_generators.js';

/**
 * Generate code based on the app structure
 * @param {Object} appData - The app data structure
 * @param {string} format - The format to generate (react, html, css, etc.)
 * @param {Object} options - Generation options
 * @returns {string|Object} - The generated code or project structure
 */
export const generateCode = (appData, format = 'react', options = {}) => {
  const defaultOptions = {
    typescript: false,
    includeAccessibility: true,
    includeTests: false,
    includeStorybook: false,
    styleFramework: 'styled-components', // 'styled-components', 'emotion', 'tailwind', 'css-modules'
    stateManagement: 'useState', // 'useState', 'redux', 'zustand', 'context'
    projectStructure: 'single-file', // 'single-file', 'multi-file', 'full-project'
    bundler: 'vite', // 'vite', 'webpack', 'parcel'
    packageManager: 'npm', // 'npm', 'yarn', 'pnpm'
    ...options
  };

  switch (format) {
    case 'react':
      return generateReactCode(appData, defaultOptions);
    case 'react-ts':
      return generateReactCode(appData, { ...defaultOptions, typescript: true });
    case 'vue':
      return generateVueCode(appData, defaultOptions);
    case 'vue-ts':
      return generateVueCode(appData, { ...defaultOptions, typescript: true });
    case 'angular':
      return generateAngularCode(appData, defaultOptions);
    case 'svelte':
      return generateSvelteCode(appData, defaultOptions);
    case 'next':
      return generateNextJSCode(appData, defaultOptions);
    case 'nuxt':
      return generateNuxtCode(appData, defaultOptions);
    case 'html':
      return generateHtmlCode(appData, defaultOptions);
    case 'css':
      return generateCssCode(appData, defaultOptions);
    case 'react-native':
      return generateReactNativeCode(appData, defaultOptions);
    case 'flutter':
      return generateFlutterCode(appData, defaultOptions);
    case 'ionic':
      return generateIonicCode(appData, defaultOptions);
    default:
      return JSON.stringify(appData, null, 2);
  }
};

/**
 * Generate enhanced React code with modern best practices
 * @param {Object} appData - The app data structure
 * @param {Object} options - Generation options
 * @returns {string|Object} - The generated React code or project structure
 */
const generateReactCode = (appData, options = {}) => {
  const { components, layouts, styles, data } = appData;
  const { typescript, includeAccessibility, projectStructure, styleFramework, stateManagement } = options;

  if (projectStructure === 'full-project') {
    return generateReactProject(appData, options);
  }

  // Generate imports
  let imports = generateReactImports(components, options);

  // Generate TypeScript interfaces if needed
  let interfaces = '';
  if (typescript) {
    interfaces = generateTypeScriptInterfaces(components, layouts);
  }

  // Generate styled components or CSS
  let stylesCode = generateReactStyles(styles, components, layouts, options);

  // Generate component definitions
  let componentDefinitions = generateReactComponents(components, options);

  // Generate main App component
  let appComponent = generateReactAppComponent(components, layouts, data, options);

  // Combine all parts
  const fileExtension = typescript ? 'tsx' : 'jsx';
  const code = `${imports}
${interfaces}
${stylesCode}
${componentDefinitions}
${appComponent}`;

  if (projectStructure === 'single-file') {
    return code;
  } else {
    return {
      [`App.${fileExtension}`]: code,
      'package.json': generatePackageJson('react-app', options),
      'README.md': generateReadme('React', options),
      ...(typescript && { 'tsconfig.json': generateTSConfig() })
    };
  }
};

/**
 * Generate React imports based on components and options
 */
const generateReactImports = (components, options) => {
  const { typescript, styleFramework, stateManagement } = options;
  let imports = `import React${stateManagement === 'useState' ? ', { useState, useEffect }' : ''} from 'react';\n`;

  // Style framework imports
  if (styleFramework === 'styled-components') {
    imports += `import styled from 'styled-components';\n`;
  } else if (styleFramework === 'emotion') {
    imports += `import styled from '@emotion/styled';\n`;
  }

  // State management imports
  if (stateManagement === 'redux') {
    imports += `import { useSelector, useDispatch } from 'react-redux';\n`;
  } else if (stateManagement === 'zustand') {
    imports += `import { useStore } from './store';\n`;
  }

  // PropTypes for non-TypeScript projects
  if (!typescript) {
    imports += `import PropTypes from 'prop-types';\n`;
  }

  return imports + '\n';
};

/**
 * Generate TypeScript interfaces
 */
const generateTypeScriptInterfaces = (components, layouts) => {
  let interfaces = '// TypeScript Interfaces\n';

  // Component props interfaces
  const componentTypes = [...new Set(components.map(c => c.type))];
  componentTypes.forEach(type => {
    const component = components.find(c => c.type === type);
    if (component && component.props) {
      interfaces += `interface ${type}Props {\n`;
      Object.entries(component.props).forEach(([prop, value]) => {
        const propType = typeof value === 'string' ? 'string' :
          typeof value === 'number' ? 'number' :
            typeof value === 'boolean' ? 'boolean' : 'any';
        interfaces += `  ${prop}?: ${propType};\n`;
      });
      interfaces += `  className?: string;\n`;
      interfaces += `  children?: React.ReactNode;\n`;
      interfaces += `}\n\n`;
    }
  });

  // App data interface
  interfaces += `interface AppData {\n`;
  interfaces += `  components: ComponentData[];\n`;
  interfaces += `  layouts: LayoutData[];\n`;
  interfaces += `}\n\n`;

  return interfaces;
};

/**
 * Generate React styles based on framework choice
 */
const generateReactStyles = (styles, components, layouts, options) => {
  const { styleFramework } = options;

  if (styleFramework === 'styled-components' || styleFramework === 'emotion') {
    return generateStyledComponents(styles, components, layouts);
  } else if (styleFramework === 'tailwind') {
    return '// Tailwind CSS classes will be used inline\n\n';
  } else {
    return generateCSSModules(styles);
  }
};

/**
 * Generate styled-components
 */
const generateStyledComponents = (styles, components, layouts) => {
  let styledCode = '// Styled Components\n';

  // Container styles
  styledCode += `const AppContainer = styled.div\`
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
\`;\n\n`;

  // Layout styles
  layouts.forEach(layout => {
    const layoutName = layout.name || layout.type || 'Layout';
    styledCode += `const ${pascalCase(layoutName)}Container = styled.div\`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  ${layout.styles ? Object.entries(layout.styles).map(([prop, value]) =>
      `${kebabToCamelCase(prop)}: ${value};`).join('\n  ') : ''}
\`;\n\n`;
  });

  // Component styles
  const componentTypes = [...new Set(components.map(c => c.type))];
  componentTypes.forEach(type => {
    styledCode += `const Styled${type} = styled.div\`
  ${generateComponentStyles(type)}
\`;\n\n`;
  });

  return styledCode;
};

/**
 * Generate HTML code
 * @param {Object} appData - The app data structure
 * @param {Object} options - Generation options
 * @returns {string} - The generated HTML code
 */
const generateHtmlCode = (appData, options = {}) => {
  const { components, layouts, styles } = appData;

  // Generate CSS
  let css = '';
  Object.entries(styles).forEach(([selector, style]) => {
    css += `${selector} {\n`;
    Object.entries(style).forEach(([prop, value]) => {
      css += `  ${prop}: ${value};\n`;
    });
    css += '}\n\n';
  });

  // Generate HTML
  let html = '';
  layouts.forEach(layout => {
    html += `<div class="${layout.type}">\n`;

    layout.components.forEach(componentId => {
      const component = components.find(c => c.id === componentId);
      if (component) {
        switch (component.type) {
          case 'Button':
            html += `  <button class="${component.props.className || ''}">${component.props.text || 'Button'}</button>\n`;
            break;
          case 'Input':
            html += `  <input type="${component.props.type || 'text'}" placeholder="${component.props.placeholder || ''}" class="${component.props.className || ''}" />\n`;
            break;
          case 'Text':
            html += `  <p class="${component.props.className || ''}">${component.props.content || ''}</p>\n`;
            break;
          case 'Image':
            html += `  <img src="${component.props.src || ''}" alt="${component.props.alt || ''}" class="${component.props.className || ''}" />\n`;
            break;
          default:
            html += `  <div class="${component.type.toLowerCase()}">${component.props.content || component.type}</div>\n`;
        }
      }
    });

    html += '</div>\n';
  });

  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Generated App</title>
  <style>
${css}
  </style>
</head>
<body>
  <div class="app">
${html}
  </div>
</body>
</html>
`;
};

/**
 * Generate CSS code
 * @param {Object} appData - The app data structure
 * @returns {string} - The generated CSS code
 */
const generateCssCode = (appData) => {
  const { styles } = appData;

  let css = '';
  Object.entries(styles).forEach(([selector, style]) => {
    css += `${selector} {\n`;
    Object.entries(style).forEach(([prop, value]) => {
      css += `  ${prop}: ${value};\n`;
    });
    css += '}\n\n';
  });

  return css;
};

/**
 * Generate React component definitions
 */
const generateReactComponents = (components, options) => {
  const { typescript, includeAccessibility } = options;
  let componentCode = '// Component Definitions\n';

  const componentTypes = [...new Set(components.map(c => c.type))];
  componentTypes.forEach(type => {
    const component = components.find(c => c.type === type);
    const propsType = typescript ? `${type}Props` : '';

    componentCode += `const ${type} = (${typescript ? `props: ${propsType}` : 'props'}) => {\n`;
    componentCode += `  const { ${Object.keys(component.props || {}).join(', ')}, className, children, ...rest } = props;\n\n`;

    componentCode += `  return (\n`;
    componentCode += `    <Styled${type}\n`;
    componentCode += `      className={className}\n`;
    if (includeAccessibility) {
      componentCode += `      role="${getAriaRole(type)}"\n`;
      if (type === 'Button') {
        componentCode += `      aria-label={props['aria-label'] || props.text || 'Button'}\n`;
      }
    }
    componentCode += `      {...rest}\n`;
    componentCode += `    >\n`;
    componentCode += `      ${generateComponentContent(type, component.props)}\n`;
    componentCode += `      {children}\n`;
    componentCode += `    </Styled${type}>\n`;
    componentCode += `  );\n`;
    componentCode += `};\n\n`;

    // Add PropTypes for non-TypeScript projects
    if (!typescript) {
      componentCode += `${type}.propTypes = {\n`;
      Object.entries(component.props || {}).forEach(([prop, value]) => {
        const propType = typeof value === 'string' ? 'PropTypes.string' :
          typeof value === 'number' ? 'PropTypes.number' :
            typeof value === 'boolean' ? 'PropTypes.bool' : 'PropTypes.any';
        componentCode += `  ${prop}: ${propType},\n`;
      });
      componentCode += `  className: PropTypes.string,\n`;
      componentCode += `  children: PropTypes.node\n`;
      componentCode += `};\n\n`;
    }
  });

  return componentCode;
};

/**
 * Generate React App component
 */
const generateReactAppComponent = (components, layouts, data, options) => {
  const { typescript, stateManagement, includeAccessibility } = options;

  let appCode = '// Main App Component\n';

  // State management setup
  if (stateManagement === 'useState' && data && Object.keys(data).length > 0) {
    appCode += `const App = () => {\n`;
    Object.entries(data).forEach(([key, value]) => {
      appCode += `  const [${key}, set${pascalCase(key)}] = useState(${JSON.stringify(value)});\n`;
    });
    appCode += '\n';
  } else {
    appCode += `const App = () => {\n`;
  }

  appCode += `  return (\n`;
  appCode += `    <AppContainer${includeAccessibility ? ' role="main"' : ''}>\n`;

  // Generate layout structure
  layouts.forEach(layout => {
    const layoutName = layout.name || layout.type || 'Layout';
    appCode += `      <${pascalCase(layoutName)}Container>\n`;

    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(componentId => {
        const component = components.find(c => c.id === componentId);
        if (component) {
          appCode += `        <${component.type}`;
          Object.entries(component.props || {}).forEach(([prop, value]) => {
            if (typeof value === 'string') {
              appCode += `\n          ${prop}="${value}"`;
            } else {
              appCode += `\n          ${prop}={${JSON.stringify(value)}}`;
            }
          });
          appCode += `\n        />\n`;
        }
      });
    } else {
      appCode += `        {/* Add components here */}\n`;
    }

    appCode += `      </${pascalCase(layoutName)}Container>\n`;
  });

  appCode += `    </AppContainer>\n`;
  appCode += `  );\n`;
  appCode += `};\n\n`;
  appCode += `export default App;\n`;

  return appCode;
};

/**
 * Utility functions
 */

/**
 * Convert kebab-case to camelCase
 * @param {string} str - The string to convert
 * @returns {string} - The camelCase string
 */
const camelCase = (str) => {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
};

/**
 * Convert string to PascalCase
 */
const pascalCase = (str) => {
  return str.replace(/(?:^|[\s-_])(\w)/g, (match, letter) => letter.toUpperCase()).replace(/[\s-_]/g, '');
};

/**
 * Convert kebab-case to camelCase for CSS properties
 */
const kebabToCamelCase = (str) => {
  return str.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
};

/**
 * Get appropriate ARIA role for component type
 */
const getAriaRole = (type) => {
  const roleMap = {
    'Button': 'button',
    'Input': 'textbox',
    'Text': 'text',
    'Image': 'img',
    'Header': 'banner',
    'Section': 'region',
    'Card': 'article',
    'List': 'list'
  };
  return roleMap[type] || 'generic';
};

/**
 * Generate component content based on type
 */
const generateComponentContent = (type, props) => {
  switch (type) {
    case 'Button':
      return props?.text || 'Button';
    case 'Text':
      return props?.content || 'Text content';
    case 'Input':
      return ''; // Self-closing
    case 'Image':
      return ''; // Self-closing
    default:
      return props?.content || type;
  }
};

/**
 * Generate component styles based on type
 */
const generateComponentStyles = (type) => {
  const styleMap = {
    'Button': `
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  background-color: #007bff;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: #0056b3;
  }

  &:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }`,
    'Input': `
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 0.25rem;
  font-size: 1rem;

  &:focus {
    outline: 2px solid #007bff;
    border-color: #007bff;
  }`,
    'Text': `
  margin: 0;
  line-height: 1.5;`,
    'Image': `
  max-width: 100%;
  height: auto;`,
    'Card': `
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0.5rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);`
  };

  return styleMap[type] || `
  /* Add styles for ${type} */`;
};

/**
 * Generate CSS modules styles
 */
const generateCSSModules = (styles) => {
  let css = '';
  Object.entries(styles).forEach(([selector, style]) => {
    css += `${selector} {\n`;
    Object.entries(style).forEach(([prop, value]) => {
      css += `  ${prop}: ${value};\n`;
    });
    css += '}\n\n';
  });
  return css;
};

/**
 * Generate full React project structure
 */
const generateReactProject = (appData, options) => {
  const { typescript, bundler = 'vite' } = options;
  const fileExtension = typescript ? 'tsx' : 'jsx';

  const projectStructure = {
    'package.json': generatePackageJson('react-app', options),
    'README.md': generateReadme('React', options),
    [`src/App.${fileExtension}`]: generateReactCode(appData, { ...options, projectStructure: 'single-file' }),
    [`src/index.${fileExtension}`]: generateReactIndex(typescript),
    'src/index.css': generateGlobalStyles(),
    'public/index.html': generateIndexHTML(),
    '.gitignore': generateGitIgnore(),
    '.eslintrc.json': generateESLintConfig('react', typescript),
    'Dockerfile': generateDockerfile('react'),
    'docker-compose.yml': generateDockerCompose(),
    '.github/workflows/ci.yml': generateGitHubActions('react')
  };

  if (typescript) {
    projectStructure['tsconfig.json'] = generateTSConfig();
  }

  if (bundler === 'vite') {
    projectStructure['vite.config.js'] = generateViteConfig('react', typescript);
  }

  return projectStructure;
};

/**
 * Generate React index file
 */
const generateReactIndex = (typescript) => {
  return `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root')${typescript ? ' as HTMLElement' : ''}
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;
};

/**
 * Generate global styles
 */
const generateGlobalStyles = () => {
  return `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

button {
  cursor: pointer;
}

input, textarea, select {
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
}`;
};

/**
 * Generate index.html
 */
const generateIndexHTML = () => {
  return `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Generated by App Builder" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <title>Generated App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`;
};

/**
 * Generate .gitignore
 */
const generateGitIgnore = () => {
  return `# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary folders
tmp/
temp/`;
};
