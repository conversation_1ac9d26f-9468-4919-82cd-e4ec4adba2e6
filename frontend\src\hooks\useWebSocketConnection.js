import { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { websocketConnected, websocketDisconnected, websocketMessageReceived } from '../redux/minimal-store';

// Safely import optional dependencies
let EnhancedWebSocketClient, ConnectionState, timeSync;

try {
  const wsModule = require('../services/EnhancedWebSocketClient');
  EnhancedWebSocketClient = wsModule.default || wsModule.EnhancedWebSocketClient;
  ConnectionState = wsModule.ConnectionState;
} catch (error) {
  console.warn('EnhancedWebSocketClient not available, using fallback');
}

try {
  const timeSyncModule = require('../services/TimeSyncService');
  timeSync = timeSyncModule.timeSync || timeSyncModule.default;
} catch (error) {
  console.warn('TimeSyncService not available, using fallback');
}

/**
 * Custom hook for WebSocket integration with better error handling
 * 
 * @param {Object} options - WebSocket options
 * @param {string} options.url - WebSocket URL
 * @param {boolean} [options.autoConnect=true] - Whether to connect automatically
 * @param {boolean} [options.autoReconnect=true] - Whether to reconnect automatically
 * @param {boolean} [options.updateRedux=true] - Whether to update Redux state
 * @param {boolean} [options.debug=false] - Enable debug logging
 * @returns {Object} WebSocket state and methods
 */
function useWebSocketConnection(options) {
  const {
    url,
    autoConnect = true,
    autoReconnect = true,
    updateRedux = true,
    debug = false,
    ...wsOptions
  } = options;

  const dispatch = useDispatch();
  const wsRef = useRef(null);

  const [connected, setConnected] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [messages, setMessages] = useState([]);
  const [error, setError] = useState(null);
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const [closeInfo, setCloseInfo] = useState(null);

  // Initialize WebSocket client
  useEffect(() => {
    if (!url) return;

    // Check if EnhancedWebSocketClient is available
    if (!EnhancedWebSocketClient) {
      console.warn('EnhancedWebSocketClient not available, using native WebSocket');

      // Fallback to native WebSocket
      try {
        const ws = new WebSocket(url);
        wsRef.current = ws;

        // Add basic methods for compatibility
        ws.destroy = () => ws.close();
        ws.open = () => {
          // WebSocket opens automatically on creation
        };

        return () => {
          if (wsRef.current) {
            wsRef.current.close();
            wsRef.current = null;
          }
        };
      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        setError(error);
        return;
      }
    }

    // Create WebSocket client
    const ws = new EnhancedWebSocketClient({
      url,
      autoConnect,
      autoReconnect,
      debug,
      ...wsOptions
    });

    // Store reference
    wsRef.current = ws;

    // Clean up on unmount
    return () => {
      if (wsRef.current) {
        wsRef.current.destroy();
        wsRef.current = null;
      }
    };
  }, [url, autoConnect, autoReconnect, debug]);

  // Set up event listeners
  useEffect(() => {
    const ws = wsRef.current;
    if (!ws) return;

    // Connection opened
    const handleOpen = async (event) => {
      // Only try to synchronize time if timeSync exists
      if (timeSync && typeof timeSync.synchronize === 'function') {
        try {
          await timeSync.synchronize();
        } catch (error) {
          console.warn('Time synchronization failed:', error);
        }
      }
      console.log('WebSocket connection opened:', event);
      setConnected(true);
      setConnecting(false);
      setError(null);
      setCloseInfo(null);

      if (updateRedux) {
        try {
          dispatch(websocketConnected());
        } catch (error) {
          console.warn('Failed to dispatch websocketConnected:', error);
        }
      }
    };

    // Connection closed
    const handleClose = (event) => {
      console.log('WebSocket connection closed:', event);
      setConnected(false);
      setConnecting(false);
      setCloseInfo(event);

      if (updateRedux) {
        dispatch(websocketDisconnected());
      }
    };

    // Connection error
    const handleError = (event) => {
      console.error('WebSocket error:', event);
      setError(event);
      setConnecting(false);
    };

    // Message received
    const handleMessage = useCallback((message) => {
      try {
        let processedMessage = message;

        // Handle native WebSocket message events
        if (message.data) {
          try {
            processedMessage = JSON.parse(message.data);
          } catch (error) {
            processedMessage = { content: message.data };
          }
        }

        const serverTimestamp = new Date(processedMessage.timestamp || Date.now());
        let localTimestamp = serverTimestamp;

        // Only use timeSync if available
        if (timeSync && typeof timeSync.getClientTime === 'function') {
          try {
            localTimestamp = timeSync.getClientTime(serverTimestamp);
          } catch (error) {
            console.warn('Time sync failed, using server timestamp:', error);
          }
        }

        setMessages(prev => [...prev, {
          ...processedMessage,
          timestamp: localTimestamp,
          serverTimestamp
        }]);

        // Update Redux if enabled
        if (updateRedux) {
          try {
            dispatch(websocketMessageReceived({
              type: 'received',
              content: processedMessage.content || processedMessage,
              timestamp: localTimestamp,
              serverTimestamp
            }));
          } catch (error) {
            console.warn('Failed to dispatch websocketMessageReceived:', error);
          }
        }
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    }, [dispatch, updateRedux]);

    // Reconnect attempt
    const handleReconnectAttempt = (data) => {
      console.log('WebSocket reconnect attempt:', data);
      setConnecting(true);
      setReconnectAttempt(data.attempt);
    };

    // Add event listeners with null checks
    ws.addEventListener('open', handleOpen);
    ws.addEventListener('close', handleClose);
    ws.addEventListener('error', handleError);
    ws.addEventListener('message', handleMessage);
    ws.addEventListener('reconnect_attempt', handleReconnectAttempt);

    // Clean up event listeners
    return () => {
      if (ws) {
        ws.removeEventListener('open', handleOpen);
        ws.removeEventListener('close', handleClose);
        ws.removeEventListener('error', handleError);
        ws.removeEventListener('message', handleMessage);
        ws.removeEventListener('reconnect_attempt', handleReconnectAttempt);
      }
    };
  }, [dispatch, updateRedux]);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!wsRef.current) return;

    setConnecting(true);
    wsRef.current.open();
  }, []);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (!wsRef.current) return;

    wsRef.current.close();
  }, []);

  // Send message
  const sendMessage = useCallback((data, batch = true) => {
    if (!wsRef.current) {
      return false;
    }

    // Check connection state
    const isConnected = ConnectionState
      ? wsRef.current.connectionState === ConnectionState.OPEN
      : wsRef.current.readyState === WebSocket.OPEN;

    if (!isConnected) {
      return false;
    }

    try {
      let success = false;

      // Send message using appropriate method
      if (wsRef.current.send && typeof wsRef.current.send === 'function') {
        // Enhanced WebSocket client
        success = wsRef.current.send(data, batch);
      } else {
        // Native WebSocket
        const messageStr = typeof data === 'string' ? data : JSON.stringify(data);
        wsRef.current.send(messageStr);
        success = true;
      }

      // Add to messages if successful
      if (success) {
        const timestamp = new Date().toISOString();

        // Add message to state
        setMessages(prev => [...prev, {
          type: 'sent',
          content: data,
          timestamp
        }]);

        // Update Redux if enabled
        if (updateRedux) {
          try {
            dispatch(websocketMessageReceived({
              type: 'sent',
              content: data,
              timestamp
            }));
          } catch (error) {
            console.warn('Failed to dispatch sent message:', error);
          }
        }
      }

      return success;
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      return false;
    }
  }, [dispatch, updateRedux]);

  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Reset connection (close and reopen)
  const resetConnection = useCallback(() => {
    if (!wsRef.current) return;

    wsRef.current.close();
    setTimeout(() => {
      setError(null);
      setCloseInfo(null);
      setReconnectAttempt(0);
      wsRef.current.open();
    }, 1000);
  }, []);

  return {
    connected,
    connecting,
    reconnectAttempt,
    messages,
    error,
    closeInfo,
    connect,
    disconnect,
    sendMessage,
    clearMessages,
    resetConnection,
    client: wsRef.current
  };
}

export default useWebSocketConnection;


