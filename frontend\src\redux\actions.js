// Action types
export const ADD_COMPONENT = 'ADD_COMPONENT';
export const ADD_LAYOUT = 'ADD_LAYOUT';
export const ADD_STYLE = 'ADD_STYLE';
export const ADD_DATA = 'ADD_DATA';
export const FETCH_APP_DATA_SUCCESS = 'FETCH_APP_DATA_SUCCESS';
export const FETCH_APP_DATA_ERROR = 'FETCH_APP_DATA_ERROR';

// Additional action types used in reducers
export const WS_CONNECT = 'WS_CONNECT';
export const WS_CONNECTED = 'WS_CONNECTED';
export const WS_DISCONNECTED = 'WS_DISCONNECTED';
export const WS_MESSAGE_RECEIVED = 'WS_MESSAGE_RECEIVED';
export const WS_ERROR = 'WS_ERROR';
export const UPDATE_COMPONENT = 'UPDATE_COMPONENT';
export const DELETE_COMPONENT = 'DELETE_COMPONENT';
export const UPDATE_LAYOUT = 'UPDATE_LAYOUT';
export const DELETE_LAYOUT = 'DELETE_LAYOUT';
export const SAVE_APP_DATA = 'SAVE_APP_DATA';
export const LOAD_APP_DATA = 'LOAD_APP_DATA';
export const SET_LOADING = 'SET_LOADING';
export const SET_ERROR = 'SET_ERROR';
export const CLEAR_ERROR = 'CLEAR_ERROR';

// ActionTypes object for compatibility with reducers
export const ActionTypes = {
  ADD_COMPONENT,
  ADD_LAYOUT,
  ADD_STYLE,
  ADD_DATA,
  FETCH_APP_DATA_SUCCESS,
  FETCH_APP_DATA_ERROR,
  WS_CONNECT,
  WS_CONNECTED,
  WS_DISCONNECTED,
  WS_MESSAGE_RECEIVED,
  WS_ERROR,
  UPDATE_COMPONENT,
  DELETE_COMPONENT,
  UPDATE_LAYOUT,
  DELETE_LAYOUT,
  SAVE_APP_DATA,
  LOAD_APP_DATA,
  SET_LOADING,
  SET_ERROR,
  CLEAR_ERROR,
};

// Action creators
export const addComponent = (type, props = {}) => ({
  type: ADD_COMPONENT,
  payload: { type, props }
});

export const addLayout = (layoutType, components = [], styles = {}) => ({
  type: ADD_LAYOUT,
  payload: { type: layoutType, components, styles }
});

export const addStyle = (selector, style) => ({
  type: ADD_STYLE,
  payload: { selector, style }
});

export const addData = (key, value) => ({
  type: ADD_DATA,
  payload: { key, value }
});

export const fetchAppDataSuccess = (data) => ({
  type: FETCH_APP_DATA_SUCCESS,
  payload: data
});

export const fetchAppDataError = (error) => ({
  type: FETCH_APP_DATA_ERROR,
  payload: error
});

// API Base URL
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';

// Async action creators
export const fetchAppData = () => {
  return async (dispatch) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/app-data/`);
      const data = await response.json();
      dispatch(fetchAppDataSuccess(data));
    } catch (error) {
      console.error('Error fetching app data:', error);
      dispatch(fetchAppDataError(error.message));
    }
  };
};

export const saveAppData = (appData) => {
  return async (dispatch) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/app-data/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(appData),
      });
      const data = await response.json();
      dispatch(fetchAppDataSuccess(data));
    } catch (error) {
      console.error('Error saving app data:', error);
      dispatch(fetchAppDataError(error.message));
    }
  };
};

// Additional action creators for missing functions
export const updateComponent = (index, updates) => ({
  type: UPDATE_COMPONENT,
  payload: { index, updates }
});

export const deleteComponent = (index) => ({
  type: DELETE_COMPONENT,
  payload: { index }
});

export const updateLayout = (index, updates) => ({
  type: UPDATE_LAYOUT,
  payload: { index, updates }
});

export const removeLayout = (index) => ({
  type: DELETE_LAYOUT,
  payload: { index }
});

export const setLoading = (loading) => ({
  type: SET_LOADING,
  payload: loading
});

export const setError = (error) => ({
  type: SET_ERROR,
  payload: error
});

export const clearError = () => ({
  type: CLEAR_ERROR
});

// Project management actions
export const loadProjects = () => ({
  type: 'LOAD_PROJECTS'
});

export const setActiveProject = (projectId) => ({
  type: 'SET_ACTIVE_PROJECT',
  payload: projectId
});

export const updateProject = (project) => ({
  type: 'UPDATE_PROJECT',
  payload: project
});

export const createProject = (project) => ({
  type: 'CREATE_PROJECT',
  payload: project
});

export const deleteProject = (projectId) => ({
  type: 'DELETE_PROJECT',
  payload: projectId
});

// Theme management actions
export const addTheme = (theme) => ({
  type: 'ADD_THEME',
  payload: theme
});

export const updateTheme = (theme) => ({
  type: 'UPDATE_THEME',
  payload: theme
});

export const removeTheme = (themeId) => ({
  type: 'REMOVE_THEME',
  payload: { id: themeId }
});

export const setActiveTheme = (themeId) => ({
  type: 'SET_ACTIVE_THEME',
  payload: themeId
});

// User management actions
export const setUser = (user) => ({
  type: 'SET_USER',
  payload: user
});

// WebSocket action creators
export const wsConnect = (url) => ({
  type: WS_CONNECT,
  payload: { url }
});

export const wsConnected = () => ({
  type: WS_CONNECTED
});

export const wsDisconnected = (error = null) => ({
  type: WS_DISCONNECTED,
  payload: { error }
});

export const wsMessageReceived = (message) => ({
  type: WS_MESSAGE_RECEIVED,
  payload: message
});

export const wsError = (error) => ({
  type: WS_ERROR,
  payload: error
});
