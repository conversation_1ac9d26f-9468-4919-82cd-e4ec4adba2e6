"""
WebSocket URL routing for App Builder 201
"""

from django.urls import re_path
from .consumers import (
    AppBuilderConsumer,
    CollaborationConsumer,
    NotificationConsumer,
    PerformanceMonitorConsumer
)
from .consumers.ai_suggestions import AISuggestionsConsumer

websocket_urlpatterns = [
    # App Builder routes
    re_path(r'^ws/$', AppBuilderConsumer.as_asgi()),
    re_path(r'^ws/app/$', AppBuilderConsumer.as_asgi()),
    re_path(r'^ws/app/(?P<app_id>\w+)/$', AppBuilderConsumer.as_asgi()),
    re_path(r'^ws/test/$', AppBuilderConsumer.as_asgi()),

    # Collaboration routes
    re_path(r'^ws/collaboration/$', CollaborationConsumer.as_asgi()),
    re_path(r'^ws/collaboration/(?P<session_id>\w+)/$', CollaborationConsumer.as_asgi()),

    # Notification routes
    re_path(r'^ws/notifications/$', NotificationConsumer.as_asgi()),

    # Performance monitoring routes
    re_path(r'^ws/performance/$', PerformanceMonitorConsumer.as_asgi()),

    # AI suggestions routes
    re_path(r'^ws/ai-suggestions/$', AISuggestionsConsumer.as_asgi()),
    re_path(r'^ws/ai/$', AISuggestionsConsumer.as_asgi()),
]
