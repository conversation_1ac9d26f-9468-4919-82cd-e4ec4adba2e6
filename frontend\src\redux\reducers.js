/**
 * Redux Reducers for the App Builder
 */
import { ActionTypes } from './actions';

// Initial state
const initialState = {
  // App data structure
  app: {
    // App data
    components: [],
    layouts: [],
    styles: {},
    data: {},
  },

  // WebSocket state
  websocket: {
    connected: false,
    connecting: false,
    url: null,
    error: null,
    lastMessage: null,
    reconnectAttempts: 0,
  },

  // UI state
  loading: false,
  error: null,

  // Theme state
  themes: [],
  activeTheme: 'default',
};

/**
 * Root reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
const rootReducer = (state = initialState, action) => {
  switch (action.type) {
    // WebSocket actions
    case ActionTypes.WS_CONNECT:
      return {
        ...state,
        websocket: {
          ...state.websocket,
          connecting: true,
          url: action.payload?.url || state.websocket.url,
          error: null,
        },
      };

    case ActionTypes.WS_CONNECTED:
      return {
        ...state,
        websocket: {
          ...state.websocket,
          connected: true,
          connecting: false,
          error: null,
          reconnectAttempts: 0,
        },
      };

    case ActionTypes.WS_DISCONNECTED:
      return {
        ...state,
        websocket: {
          ...state.websocket,
          connected: false,
          connecting: false,
          error: action.payload?.error || null,
        },
      };

    case ActionTypes.WS_MESSAGE_RECEIVED:
      // Handle different message types
      const message = action.payload;

      switch (message.type) {
        case 'app_data':
          return {
            ...state,
            websocket: {
              ...state.websocket,
              lastMessage: message,
            },
            app: {
              ...state.app,
              components: message.data.components || [],
              layouts: message.data.layouts || [],
              styles: message.data.styles || {},
              data: message.data.data || {},
            },
          };

        default:
          return {
            ...state,
            websocket: {
              ...state.websocket,
              lastMessage: message,
            },
          };
      }

    case ActionTypes.WS_ERROR:
      return {
        ...state,
        websocket: {
          ...state.websocket,
          error: action.payload,
          connected: false,
          connecting: false,
        },
      };

    // Component actions
    case ActionTypes.ADD_COMPONENT:
      return {
        ...state,
        app: {
          ...state.app,
          components: [...state.app.components, action.payload],
        },
      };

    case ActionTypes.UPDATE_COMPONENT:
      return {
        ...state,
        app: {
          ...state.app,
          components: state.app.components.map((component, index) => {
            if (index === action.payload.index) {
              return {
                ...component,
                ...action.payload.updates,
              };
            }
            return component;
          }),
        },
      };

    case ActionTypes.DELETE_COMPONENT:
      return {
        ...state,
        app: {
          ...state.app,
          components: state.app.components.filter((_, index) => index !== action.payload.index),
        },
      };

    // Layout actions
    case ActionTypes.ADD_LAYOUT:
      return {
        ...state,
        app: {
          ...state.app,
          layouts: [...state.app.layouts, action.payload],
        },
      };

    case ActionTypes.UPDATE_LAYOUT:
      return {
        ...state,
        app: {
          ...state.app,
          layouts: state.app.layouts.map((layout, index) => {
            if (index === action.payload.index) {
              return {
                ...layout,
                ...action.payload.updates,
              };
            }
            return layout;
          }),
        },
      };

    case ActionTypes.DELETE_LAYOUT:
      return {
        ...state,
        app: {
          ...state.app,
          layouts: state.app.layouts.filter((_, index) => index !== action.payload.index),
        },
      };

    // App data actions
    case ActionTypes.SAVE_APP_DATA:
      return {
        ...state,
        app: {
          ...state.app,
          components: action.payload.components || state.app.components,
          layouts: action.payload.layouts || state.app.layouts,
          styles: action.payload.styles || state.app.styles,
          data: action.payload.data || state.app.data,
        },
      };

    case ActionTypes.LOAD_APP_DATA:
      return {
        ...state,
        app: {
          ...state.app,
          components: action.payload.components || [],
          layouts: action.payload.layouts || [],
          styles: action.payload.styles || {},
          data: action.payload.data || {},
        },
      };

    // UI actions
    case ActionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload,
      };

    case ActionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };

    case ActionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };

    // Theme actions
    case 'ADD_THEME':
      return {
        ...state,
        themes: [...state.themes, action.payload],
      };

    case 'UPDATE_THEME':
      return {
        ...state,
        themes: state.themes.map(theme =>
          theme.id === action.payload.id ? action.payload : theme
        ),
      };

    case 'REMOVE_THEME':
      return {
        ...state,
        themes: state.themes.filter(theme => theme.id !== action.payload.id),
      };

    case 'SET_ACTIVE_THEME':
      return {
        ...state,
        activeTheme: action.payload,
      };

    default:
      return state;
  }
};

export default rootReducer;
