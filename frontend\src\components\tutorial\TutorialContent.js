/**
 * Tutorial Content Definitions
 * 
 * Contains all predefined tutorials for the App Builder application,
 * organized by difficulty level and feature area.
 */

import React from 'react';
import { Typography, Space, Alert } from 'antd';
import {
  AppstoreOutlined,
  LayoutOutlined,
  BgColorsOutlined,
  CodeOutlined,
  DragOutlined,
  SettingOutlined,
  EyeOutlined,
  ApiOutlined,
  BookOutlined,
  RocketOutlined
} from '@ant-design/icons';
import {
  createTutorial,
  createTutorialStep,
  TUTORIAL_CATEGORIES,
  TUTORIAL_STEP_TYPES
} from './types';

const { Text, Title } = Typography;

// Tutorial Content Definitions
export const TUTORIAL_DEFINITIONS = {
  // BEGINNER TUTORIALS
  getting_started: createTutorial({
    id: 'getting_started',
    title: 'Getting Started with App Builder',
    description: 'Learn the basics of creating applications with our drag-and-drop interface.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 5,
    difficulty: 1,
    icon: <BookOutlined />,
    isRequired: true,
    steps: [
      createTutorialStep({
        id: 'welcome',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Welcome to App Builder!',
        content: 'App Builder lets you create applications visually using drag-and-drop components. Let\'s start with a quick tour of the interface.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'component_palette',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Component Palette',
        content: 'This is the component palette. Here you\'ll find all the building blocks for your application - buttons, text, forms, and more.',
        targetSelector: '[data-help-context="component-palette"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'preview_area',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Preview Area',
        content: 'This is your canvas! Drag components from the palette and drop them here to build your application.',
        targetSelector: '[data-help-context="preview-area"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'property_editor',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Property Editor',
        content: 'When you select a component, its properties appear here. You can customize colors, text, sizes, and behavior.',
        targetSelector: '[data-help-context="property-editor"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'first_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Try It Yourself!',
        content: 'Now try adding your first component! Drag a Button from the component palette to the preview area.',
        targetSelector: '[data-help-context="preview-area"]',
        requiredAction: 'add_component',
        validationFn: () => document.querySelectorAll('[data-component-id]').length > 0
      })
    ]
  }),

  drag_drop_basics: createTutorial({
    id: 'drag_drop_basics',
    title: 'Mastering Drag & Drop',
    description: 'Learn advanced drag-and-drop techniques for building complex layouts.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 7,
    difficulty: 2,
    icon: <DragOutlined />,
    prerequisites: ['getting_started'],
    steps: [
      createTutorialStep({
        id: 'drag_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Drag & Drop Mastery',
        content: 'Let\'s explore the powerful drag-and-drop features that make App Builder so intuitive.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'drag_component',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Dragging Components',
        content: 'Click and hold on any component in the palette, then drag it to the preview area. You\'ll see a visual indicator showing where it will be placed.',
        targetSelector: '[data-component-type="button"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'drop_zones',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Drop Zones',
        content: 'As you drag, you\'ll see highlighted drop zones. These show you where you can place the component.',
        targetSelector: '[data-help-context="preview-area"]',
        position: 'top'
      }),
      createTutorialStep({
        id: 'rearrange_components',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Rearranging Components',
        content: 'You can also rearrange existing components! Try dragging a component to a new position.',
        targetSelector: '[data-component-id]',
        requiredAction: 'rearrange_component'
      })
    ]
  }),

  component_properties: createTutorial({
    id: 'component_properties',
    title: 'Customizing Component Properties',
    description: 'Learn how to customize components using the property editor.',
    category: TUTORIAL_CATEGORIES.BEGINNER,
    estimatedDuration: 8,
    difficulty: 2,
    icon: <SettingOutlined />,
    prerequisites: ['getting_started'],
    steps: [
      createTutorialStep({
        id: 'select_component',
        type: TUTORIAL_STEP_TYPES.INTERACTIVE,
        title: 'Selecting Components',
        content: 'First, click on a component in the preview area to select it. You\'ll see it highlighted and its properties will appear on the right.',
        targetSelector: '[data-component-id]',
        requiredAction: 'select_component'
      }),
      createTutorialStep({
        id: 'basic_properties',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Basic Properties',
        content: 'Every component has basic properties like name and type. These help you organize your application.',
        targetSelector: '[data-property-group="basic"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'style_properties',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Style Properties',
        content: 'Style properties control how your component looks - colors, sizes, spacing, and more.',
        targetSelector: '[data-property-group="style"]',
        position: 'left'
      }),
      createTutorialStep({
        id: 'behavior_properties',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Behavior Properties',
        content: 'Behavior properties control how your component acts - click handlers, form validation, and interactions.',
        targetSelector: '[data-property-group="behavior"]',
        position: 'left'
      })
    ]
  }),

  // INTERMEDIATE TUTORIALS
  layout_design: createTutorial({
    id: 'layout_design',
    title: 'Creating Responsive Layouts',
    description: 'Master the layout system to create responsive, professional-looking applications.',
    category: TUTORIAL_CATEGORIES.INTERMEDIATE,
    estimatedDuration: 12,
    difficulty: 3,
    icon: <LayoutOutlined />,
    prerequisites: ['getting_started', 'component_properties'],
    steps: [
      createTutorialStep({
        id: 'layout_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Layout Design',
        content: 'Layouts help you organize components in rows, columns, and grids. Let\'s learn how to create professional layouts.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'container_components',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Container Components',
        content: 'Container components like Flex and Grid help you organize other components. They act as invisible structures that control positioning.',
        targetSelector: '[data-component-category="layout"]',
        position: 'right'
      }),
      createTutorialStep({
        id: 'responsive_design',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Responsive Design',
        content: 'Use the device preview to see how your layout looks on different screen sizes. Your layouts should work on mobile, tablet, and desktop.',
        targetSelector: '[data-device-selector]',
        position: 'bottom'
      })
    ]
  }),

  theme_customization: createTutorial({
    id: 'theme_customization',
    title: 'Theme Customization',
    description: 'Learn to create and apply custom themes to give your application a unique look.',
    category: TUTORIAL_CATEGORIES.INTERMEDIATE,
    estimatedDuration: 10,
    difficulty: 3,
    icon: <BgColorsOutlined />,
    prerequisites: ['component_properties'],
    steps: [
      createTutorialStep({
        id: 'theme_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Theme System',
        content: 'Themes let you define consistent colors, fonts, and styles across your entire application.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'theme_manager',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Theme Manager',
        content: 'Access the Theme Manager to create, edit, and apply themes to your application.',
        targetSelector: '[data-view="themes"]',
        position: 'bottom'
      })
    ]
  }),

  preview_features: createTutorial({
    id: 'preview_features',
    title: 'Real-time Preview Features',
    description: 'Explore the powerful preview system with multi-device support and real-time updates.',
    category: TUTORIAL_CATEGORIES.INTERMEDIATE,
    estimatedDuration: 8,
    difficulty: 3,
    icon: <EyeOutlined />,
    prerequisites: ['layout_design'],
    steps: [
      createTutorialStep({
        id: 'preview_modes',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Preview Modes',
        content: 'Switch between different device views to see how your application looks on mobile, tablet, and desktop.',
        targetSelector: '[data-device-controls]',
        position: 'bottom'
      }),
      createTutorialStep({
        id: 'real_time_updates',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Real-time Updates',
        content: 'Changes you make are instantly reflected in the preview. This helps you see exactly how your application will look to users.',
        autoAdvance: true,
        autoAdvanceDelay: 4000
      })
    ]
  }),

  // ADVANCED TUTORIALS
  code_export: createTutorial({
    id: 'code_export',
    title: 'Code Export & Deployment',
    description: 'Learn to export your application as code and deploy it to production.',
    category: TUTORIAL_CATEGORIES.ADVANCED,
    estimatedDuration: 15,
    difficulty: 4,
    icon: <CodeOutlined />,
    prerequisites: ['layout_design', 'theme_customization'],
    steps: [
      createTutorialStep({
        id: 'export_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Code Export',
        content: 'App Builder can export your visual design as clean, production-ready code in multiple frameworks.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'export_options',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Export Options',
        content: 'Choose from React, Vue, Angular, and more. Each export includes all your components, styles, and layouts.',
        targetSelector: '[data-export-framework]',
        position: 'left'
      })
    ]
  }),

  websocket_features: createTutorial({
    id: 'websocket_features',
    title: 'Real-time Features with WebSockets',
    description: 'Implement real-time functionality using WebSocket connections.',
    category: TUTORIAL_CATEGORIES.ADVANCED,
    estimatedDuration: 12,
    difficulty: 4,
    icon: <ApiOutlined />,
    prerequisites: ['preview_features'],
    steps: [
      createTutorialStep({
        id: 'websocket_intro',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'WebSocket Integration',
        content: 'WebSockets enable real-time communication between your application and server for live updates and collaboration.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'websocket_manager',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'WebSocket Manager',
        content: 'Use the WebSocket Manager to configure and test real-time connections.',
        targetSelector: '[data-view="websocket"]',
        position: 'bottom'
      })
    ]
  }),

  advanced_components: createTutorial({
    id: 'advanced_components',
    title: 'Advanced Component Techniques',
    description: 'Master advanced component features like custom properties and complex interactions.',
    category: TUTORIAL_CATEGORIES.ADVANCED,
    estimatedDuration: 18,
    difficulty: 5,
    icon: <RocketOutlined />,
    prerequisites: ['component_properties', 'code_export'],
    steps: [
      createTutorialStep({
        id: 'custom_properties',
        type: TUTORIAL_STEP_TYPES.MODAL,
        title: 'Custom Properties',
        content: 'Learn to create custom properties for components to extend their functionality beyond the built-in options.',
        showPrevious: false
      }),
      createTutorialStep({
        id: 'component_interactions',
        type: TUTORIAL_STEP_TYPES.HIGHLIGHT,
        title: 'Component Interactions',
        content: 'Set up complex interactions between components using events and state management.',
        targetSelector: '[data-property-type="interaction"]',
        position: 'left'
      })
    ]
  })
};

// Tutorial Categories for Organization
export const TUTORIAL_CATEGORIES_CONFIG = {
  [TUTORIAL_CATEGORIES.BEGINNER]: {
    title: 'Beginner',
    description: 'Start here if you\'re new to App Builder',
    color: '#52c41a',
    icon: <BookOutlined />,
    tutorials: ['getting_started', 'drag_drop_basics', 'component_properties']
  },
  [TUTORIAL_CATEGORIES.INTERMEDIATE]: {
    title: 'Intermediate',
    description: 'Build more complex applications',
    color: '#1890ff',
    icon: <LayoutOutlined />,
    tutorials: ['layout_design', 'theme_customization', 'preview_features']
  },
  [TUTORIAL_CATEGORIES.ADVANCED]: {
    title: 'Advanced',
    description: 'Master advanced features and deployment',
    color: '#f5222d',
    icon: <RocketOutlined />,
    tutorials: ['code_export', 'websocket_features', 'advanced_components']
  }
};

// Feature-specific tutorials
export const FEATURE_TUTORIALS = {
  'component-palette': ['getting_started', 'drag_drop_basics'],
  'preview-area': ['getting_started', 'preview_features'],
  'property-editor': ['component_properties', 'advanced_components'],
  'theme-manager': ['theme_customization'],
  'layout-designer': ['layout_design'],
  'code-export': ['code_export'],
  'websocket': ['websocket_features']
};

// Tutorial Learning Paths
export const LEARNING_PATHS = {
  complete_beginner: {
    title: 'Complete Beginner Path',
    description: 'Perfect for users who are new to visual app building',
    tutorials: ['getting_started', 'drag_drop_basics', 'component_properties', 'layout_design'],
    estimatedDuration: 32
  },
  designer_path: {
    title: 'Designer Path',
    description: 'Focus on visual design and user experience',
    tutorials: ['getting_started', 'component_properties', 'theme_customization', 'layout_design', 'preview_features'],
    estimatedDuration: 43
  },
  developer_path: {
    title: 'Developer Path',
    description: 'Learn to export code and implement advanced features',
    tutorials: ['getting_started', 'layout_design', 'code_export', 'websocket_features', 'advanced_components'],
    estimatedDuration: 62
  }
};

// Helper function to get tutorials by category
export const getTutorialsByCategory = (category) => {
  return Object.values(TUTORIAL_DEFINITIONS).filter(tutorial => tutorial.category === category);
};

// Helper function to get recommended next tutorials
export const getRecommendedTutorials = (completedTutorials = []) => {
  const available = Object.values(TUTORIAL_DEFINITIONS).filter(tutorial => {
    // Check if tutorial is already completed
    if (completedTutorials.includes(tutorial.id)) return false;
    
    // Check if prerequisites are met
    return tutorial.prerequisites.every(prereq => completedTutorials.includes(prereq));
  });
  
  // Sort by difficulty and category
  return available.sort((a, b) => {
    if (a.difficulty !== b.difficulty) return a.difficulty - b.difficulty;
    return a.category.localeCompare(b.category);
  });
};

export default TUTORIAL_DEFINITIONS;
