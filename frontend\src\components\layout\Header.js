import React from 'react';
import { Layout, Menu, Button, Space, Divider } from 'antd';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  AppstoreOutlined,
  SettingOutlined,
  LoginOutlined,
  UserAddOutlined,
} from '@ant-design/icons';
import UserProfile from '../auth/UserProfile';
import authService from '../../services/AuthService';
import styled from 'styled-components';
import ThemeSwitcher from '../ThemeSwitcher';
import WebSocketStatus from '../WebSocketStatus';

const { Header: AntHeader } = Layout;

// Styled components
const StyledHeader = styled(AntHeader)`
  display: flex;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: 1000;
`;

const Logo = styled.div`
  font-size: 20px;
  font-weight: bold;
  margin-right: 48px;

  a {
    color: #1890ff;
    text-decoration: none;
  }
`;

const StyledMenu = styled(Menu)`
  flex: 1;
  border-bottom: none;
  background: transparent;
`;

const AuthControls = styled.div`
  display: flex;
  align-items: center;
`;

/**
 * Header component
 * Main navigation header with authentication controls
 */
const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isAuthenticated = authService.isAuthenticated();

  // Get current path for active menu item
  const currentPath = location.pathname;

  // Handle login button click
  const handleLoginClick = () => {
    navigate('/auth?tab=login');
  };

  // Handle register button click
  const handleRegisterClick = () => {
    navigate('/auth?tab=register');
  };

  // Handle logout
  const handleLogout = () => {
    authService.logout();
    navigate('/');
  };

  return (
    <StyledHeader>
      <Logo>
        <Link to="/">App Builder</Link>
      </Logo>

      <StyledMenu
        theme="light"
        mode="horizontal"
        selectedKeys={[currentPath]}
      >
        <Menu.Item key="/" icon={<HomeOutlined />}>
          <Link to="/">Home</Link>
        </Menu.Item>
        <Menu.Item key="/apps" icon={<AppstoreOutlined />}>
          <Link to="/apps">My Apps</Link>
        </Menu.Item>
        <Menu.Item key="/templates" icon={<AppstoreOutlined />}>
          <Link to="/templates">Templates</Link>
        </Menu.Item>
      </StyledMenu>

      <AuthControls>
        <WebSocketStatus />
        <Divider type="vertical" style={{ margin: '0 8px' }} />
        <ThemeSwitcher />
        {isAuthenticated ? (
          <UserProfile
            showAvatar={true}
            showName={true}
            showMenu={true}
            onLogout={handleLogout}
          />
        ) : (
          <Space split={<Divider type="vertical" />}>
            <Button
              type="link"
              icon={<LoginOutlined />}
              onClick={handleLoginClick}
            >
              Login
            </Button>
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={handleRegisterClick}
            >
              Register
            </Button>
          </Space>
        )}
      </AuthControls>
    </StyledHeader>
  );
};

export default Header;
