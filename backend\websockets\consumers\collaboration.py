"""
Collaboration WebSocket consumer
"""

import json
import logging
import uuid
from datetime import datetime
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.utils import timezone
from .base import BaseConsumer

logger = logging.getLogger(__name__)

class CollaborationConsumer(BaseConsumer):
    """
    WebSocket consumer for real-time collaboration
    """
    # Set the default group name
    group_name = "collaboration"
    
    # Require authentication for collaboration
    auth_required = True
    
    async def connect(self):
        """
        Handle connection with session_id from URL if available
        """
        # Get session_id from URL route if available
        self.session_id = self.scope.get('url_route', {}).get('kwargs', {}).get('session_id')
        
        # If session_id is provided, use it to create a specific group
        if self.session_id:
            self.group_name = f"collaboration_{self.session_id}"
            
        # Call the parent connect method
        await super().connect()
        
        # Store user information
        self.user_id = self.scope.get('user').id if self.scope.get('user') else None
        self.username = self.scope.get('user').username if self.scope.get('user') else 'Anonymous'
        
        # If we have a session_id, announce the user's presence
        if self.session_id:
            await self.broadcast_to_group('user_joined', {
                'session_id': self.session_id,
                'user_id': self.user_id,
                'username': self.username,
                'timestamp': datetime.now().isoformat(),
            })
            
            # Send the current session state to the new user
            session_data = await self.get_session_data(self.session_id)
            await self.send_json({
                'type': 'session_data',
                'session_id': self.session_id,
                'data': session_data,
                'timestamp': datetime.now().isoformat(),
            })
            
            # Send the list of active users
            active_users = await self.get_active_users(self.session_id)
            await self.send_json({
                'type': 'active_users',
                'session_id': self.session_id,
                'users': active_users,
                'timestamp': datetime.now().isoformat(),
            })

    async def disconnect(self, close_code):
        """
        Handle disconnection and announce user's departure
        """
        # If we have a session_id, announce the user's departure
        if hasattr(self, 'session_id') and self.session_id:
            await self.broadcast_to_group('user_left', {
                'session_id': self.session_id,
                'user_id': self.user_id,
                'username': self.username,
                'timestamp': datetime.now().isoformat(),
            })
            
            # Remove user from active users
            await self.remove_active_user(self.session_id, self.user_id)
        
        # Call the parent disconnect method
        await super().disconnect(close_code)

    async def handle_message(self, message_type, data):
        """
        Handle collaboration-specific messages
        """
        if message_type == 'cursor_position':
            await self.handle_cursor_position(data)
        elif message_type == 'selection_change':
            await self.handle_selection_change(data)
        elif message_type == 'edit':
            await self.handle_edit(data)
        elif message_type == 'chat_message':
            await self.handle_chat_message(data)
        elif message_type == 'presence_update':
            await self.handle_presence_update(data)
        elif message_type == 'get_session_data':
            await self.handle_get_session_data(data)
        elif message_type == 'join_session':
            await self.handle_join_session(data)
        elif message_type == 'leave_session':
            await self.handle_leave_session(data)
        elif message_type == 'create_comment':
            await self.handle_create_comment(data)
        elif message_type == 'update_comment':
            await self.handle_update_comment(data)
        elif message_type == 'resolve_comment':
            await self.handle_resolve_comment(data)
        elif message_type == 'component_operation':
            await self.handle_component_operation(data)
        else:
            # For unhandled message types, call the parent method
            await super().handle_message(message_type, data)

    async def handle_cursor_position(self, data):
        """
        Handle cursor_position message
        """
        # Update user presence in database
        await self.update_participant_presence(
            self.session_id,
            self.user_id,
            cursor_position=data.get('position')
        )

        # Broadcast cursor position to all clients in the session
        await self.broadcast_to_group('cursor_position', {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'username': self.username,
            'position': data.get('position'),
            'timestamp': datetime.now().isoformat(),
        })

    async def handle_selection_change(self, data):
        """
        Handle selection_change message
        """
        # Update user presence in database
        await self.update_participant_presence(
            self.session_id,
            self.user_id,
            selected_component_id=data.get('selection', {}).get('component_id')
        )

        # Broadcast selection change to all clients in the session
        await self.broadcast_to_group('selection_change', {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'username': self.username,
            'selection': data.get('selection'),
            'timestamp': datetime.now().isoformat(),
        })

    async def handle_edit(self, data):
        """
        Handle edit message
        """
        # Save the edit to the database
        success = await self.save_edit(
            self.session_id, 
            self.user_id,
            data.get('edit_type'),
            data.get('position'),
            data.get('content'),
            data.get('metadata', {})
        )
        
        if success:
            # Broadcast the edit to all clients in the session
            await self.broadcast_to_group('edit', {
                'session_id': self.session_id,
                'user_id': self.user_id,
                'username': self.username,
                'edit_type': data.get('edit_type'),
                'position': data.get('position'),
                'content': data.get('content'),
                'metadata': data.get('metadata', {}),
                'timestamp': datetime.now().isoformat(),
            })
            
            # Send confirmation to the client that initiated the edit
            await self.send_json({
                'type': 'edit_confirmed',
                'edit_id': success,
                'timestamp': datetime.now().isoformat(),
                'request_id': data.get('request_id'),
            })
        else:
            await self.send_error('Failed to save edit')

    async def handle_chat_message(self, data):
        """
        Handle chat_message message
        """
        message = data.get('message')
        
        if not message:
            await self.send_error('Missing message content')
            return
            
        # Save the chat message to the database
        message_id = await self.save_chat_message(
            self.session_id,
            self.user_id,
            message
        )
        
        if message_id:
            # Broadcast the chat message to all clients in the session
            await self.broadcast_to_group('chat_message', {
                'session_id': self.session_id,
                'user_id': self.user_id,
                'username': self.username,
                'message': message,
                'message_id': message_id,
                'timestamp': datetime.now().isoformat(),
            })
        else:
            await self.send_error('Failed to save chat message')

    async def handle_presence_update(self, data):
        """
        Handle presence_update message
        """
        status = data.get('status')
        
        if not status:
            await self.send_error('Missing status parameter')
            return
            
        # Update user's presence status
        await self.update_user_presence(
            self.session_id,
            self.user_id,
            status
        )
        
        # Broadcast the presence update to all clients in the session
        await self.broadcast_to_group('presence_update', {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'username': self.username,
            'status': status,
            'timestamp': datetime.now().isoformat(),
        })

    async def handle_get_session_data(self, data):
        """
        Handle get_session_data message
        """
        session_id = data.get('session_id', self.session_id)
        
        if not session_id:
            await self.send_error('Missing session_id parameter')
            return
            
        session_data = await self.get_session_data(session_id)
        
        await self.send_json({
            'type': 'session_data',
            'session_id': session_id,
            'data': session_data,
            'timestamp': datetime.now().isoformat(),
            'request_id': data.get('request_id'),
        })

    async def handle_join_session(self, data):
        """
        Handle user joining a collaboration session
        """
        session_id = data.get('session_id')
        if not session_id:
            await self.send_error('Missing session_id parameter')
            return

        # Add user to session
        participant = await self.add_session_participant(session_id, self.user_id)
        if participant:
            # Update group membership
            self.session_id = session_id
            self.group_name = f"collaboration_{session_id}"
            await self.channel_layer.group_add(self.group_name, self.channel_name)

            # Broadcast user joined
            await self.broadcast_to_group('user_joined', {
                'session_id': session_id,
                'user_id': self.user_id,
                'username': self.username,
                'timestamp': datetime.now().isoformat(),
            })

            # Send session data to new participant
            session_data = await self.get_session_data(session_id)
            await self.send_json({
                'type': 'session_joined',
                'session_id': session_id,
                'data': session_data,
                'timestamp': datetime.now().isoformat(),
            })
        else:
            await self.send_error('Failed to join session')

    async def handle_leave_session(self, data):
        """
        Handle user leaving a collaboration session
        """
        if not self.session_id:
            await self.send_error('Not in a session')
            return

        # Remove user from session
        await self.remove_session_participant(self.session_id, self.user_id)

        # Broadcast user left
        await self.broadcast_to_group('user_left', {
            'session_id': self.session_id,
            'user_id': self.user_id,
            'username': self.username,
            'timestamp': datetime.now().isoformat(),
        })

        # Remove from group
        await self.channel_layer.group_discard(self.group_name, self.channel_name)
        self.session_id = None

    async def handle_create_comment(self, data):
        """
        Handle creating a new comment
        """
        content = data.get('content')
        component_id = data.get('component_id')
        canvas_position = data.get('canvas_position', {})
        parent_id = data.get('parent_id')

        if not content:
            await self.send_error('Missing comment content')
            return

        # Create comment in database
        comment = await self.create_comment(
            self.session_id,
            self.user_id,
            content,
            component_id,
            canvas_position,
            parent_id
        )

        if comment:
            # Broadcast new comment
            await self.broadcast_to_group('comment_created', {
                'session_id': self.session_id,
                'comment': comment,
                'timestamp': datetime.now().isoformat(),
            })
        else:
            await self.send_error('Failed to create comment')

    async def handle_update_comment(self, data):
        """
        Handle updating an existing comment
        """
        comment_id = data.get('comment_id')
        content = data.get('content')

        if not comment_id or not content:
            await self.send_error('Missing comment_id or content')
            return

        # Update comment in database
        comment = await self.update_comment(comment_id, self.user_id, content)

        if comment:
            # Broadcast comment update
            await self.broadcast_to_group('comment_updated', {
                'session_id': self.session_id,
                'comment': comment,
                'timestamp': datetime.now().isoformat(),
            })
        else:
            await self.send_error('Failed to update comment')

    async def handle_resolve_comment(self, data):
        """
        Handle resolving a comment
        """
        comment_id = data.get('comment_id')

        if not comment_id:
            await self.send_error('Missing comment_id')
            return

        # Resolve comment in database
        comment = await self.resolve_comment(comment_id, self.user_id)

        if comment:
            # Broadcast comment resolution
            await self.broadcast_to_group('comment_resolved', {
                'session_id': self.session_id,
                'comment': comment,
                'timestamp': datetime.now().isoformat(),
            })
        else:
            await self.send_error('Failed to resolve comment')

    async def handle_component_operation(self, data):
        """
        Handle component operations (add, update, delete, move)
        """
        operation_type = data.get('operation_type')
        target_id = data.get('target_id')
        operation_data = data.get('operation_data', {})

        if not operation_type or not target_id:
            await self.send_error('Missing operation_type or target_id')
            return

        # Create edit operation in database
        operation = await self.create_edit_operation(
            self.session_id,
            self.user_id,
            operation_type,
            target_id,
            operation_data
        )

        if operation:
            # Broadcast operation to all clients
            await self.broadcast_to_group('component_operation', {
                'session_id': self.session_id,
                'operation': operation,
                'timestamp': datetime.now().isoformat(),
            })
        else:
            await self.send_error('Failed to create operation')

    @database_sync_to_async
    def authenticate(self):
        """
        Authenticate the WebSocket connection
        """
        # Get the user from the scope
        user = self.scope.get('user')
        
        # Check if the user is authenticated
        if user and user.is_authenticated:
            return True
            
        # Check for token authentication
        query_string = self.scope.get('query_string', b'').decode()
        if 'token=' in query_string:
            # Extract the token
            token = query_string.split('token=')[1].split('&')[0]
            
            # Validate the token (placeholder)
            # In a real app, you would validate the token against your database
            if token == 'valid_token':
                return True
                
        return False

    @database_sync_to_async
    def get_session_data(self, session_id):
        """
        Get session data from the database
        """
        try:
            from my_app.models import CollaborationSession, SessionParticipant, Comment

            session = CollaborationSession.objects.get(id=session_id)
            participants = SessionParticipant.objects.filter(session=session, is_active=True)
            comments = Comment.objects.filter(session=session, status='open')

            return {
                'id': str(session.id),
                'name': session.name,
                'app_id': session.app.id,
                'app_name': session.app.name,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'participants': [
                    {
                        'user_id': p.user.id,
                        'username': p.user.username,
                        'role': p.role,
                        'cursor_position': p.cursor_position,
                        'selected_component_id': p.selected_component_id,
                        'current_view': p.current_view,
                        'last_seen': p.last_seen.isoformat(),
                    }
                    for p in participants
                ],
                'comments': [
                    {
                        'id': str(c.id),
                        'author': c.author.username,
                        'content': c.content,
                        'component_id': c.component_id,
                        'canvas_position': c.canvas_position,
                        'created_at': c.created_at.isoformat(),
                        'reply_count': c.reply_count,
                    }
                    for c in comments
                ],
            }
        except Exception as e:
            logger.exception(f"Error getting session data: {str(e)}")
            return {}

    @database_sync_to_async
    def get_active_users(self, session_id):
        """
        Get active users for a session
        """
        try:
            # This is a placeholder - in a real app, you would query your database
            # For now, return mock data
            return [
                {
                    'user_id': '1',
                    'username': 'user1',
                    'status': 'active',
                    'last_active': datetime.now().isoformat(),
                },
                {
                    'user_id': '2',
                    'username': 'user2',
                    'status': 'idle',
                    'last_active': datetime.now().isoformat(),
                },
            ]
        except Exception as e:
            logger.exception(f"Error getting active users: {str(e)}")
            return []

    @database_sync_to_async
    def remove_active_user(self, session_id, user_id):
        """
        Remove a user from the active users list
        """
        try:
            # This is a placeholder - in a real app, you would update your database
            logger.info(f"Removing user {user_id} from active users in session {session_id}")
            return True
        except Exception as e:
            logger.exception(f"Error removing active user: {str(e)}")
            return False

    @database_sync_to_async
    def save_edit(self, session_id, user_id, edit_type, position, content, metadata):
        """
        Save an edit to the database
        """
        try:
            # This is a placeholder - in a real app, you would update your database
            # For now, generate a mock ID
            import uuid
            edit_id = str(uuid.uuid4())
            logger.info(f"Saving edit by user {user_id} in session {session_id}: {edit_type} at {position}")
            return edit_id
        except Exception as e:
            logger.exception(f"Error saving edit: {str(e)}")
            return None

    @database_sync_to_async
    def save_chat_message(self, session_id, user_id, message):
        """
        Save a chat message to the database
        """
        try:
            # This is a placeholder - in a real app, you would update your database
            # For now, generate a mock ID
            import uuid
            message_id = str(uuid.uuid4())
            logger.info(f"Saving chat message by user {user_id} in session {session_id}: {message}")
            return message_id
        except Exception as e:
            logger.exception(f"Error saving chat message: {str(e)}")
            return None

    @database_sync_to_async
    def update_user_presence(self, session_id, user_id, status):
        """
        Update a user's presence status
        """
        try:
            from my_app.models import SessionParticipant

            participant = SessionParticipant.objects.get(
                session_id=session_id,
                user_id=user_id
            )
            participant.last_seen = timezone.now()
            participant.save(update_fields=['last_seen'])

            logger.info(f"Updated presence for user {user_id} in session {session_id}: {status}")
            return True
        except Exception as e:
            logger.exception(f"Error updating user presence: {str(e)}")
            return False

    @database_sync_to_async
    def update_participant_presence(self, session_id, user_id, cursor_position=None, selected_component_id=None, current_view=None):
        """
        Update participant presence information
        """
        try:
            from my_app.models import SessionParticipant

            participant = SessionParticipant.objects.get(
                session_id=session_id,
                user_id=user_id
            )
            participant.update_presence(cursor_position, selected_component_id, current_view)
            return True
        except Exception as e:
            logger.exception(f"Error updating participant presence: {str(e)}")
            return False

    @database_sync_to_async
    def add_session_participant(self, session_id, user_id):
        """
        Add a user as a participant to a session
        """
        try:
            from my_app.models import CollaborationSession, SessionParticipant

            session = CollaborationSession.objects.get(id=session_id)
            user = User.objects.get(id=user_id)

            participant, created = SessionParticipant.objects.get_or_create(
                session=session,
                user=user,
                defaults={'role': 'editor', 'is_active': True}
            )

            if not created:
                participant.is_active = True
                participant.last_seen = timezone.now()
                participant.save(update_fields=['is_active', 'last_seen'])

            return {
                'user_id': participant.user.id,
                'username': participant.user.username,
                'role': participant.role,
                'joined_at': participant.joined_at.isoformat(),
            }
        except Exception as e:
            logger.exception(f"Error adding session participant: {str(e)}")
            return None

    @database_sync_to_async
    def remove_session_participant(self, session_id, user_id):
        """
        Remove a user from a session
        """
        try:
            from my_app.models import SessionParticipant

            participant = SessionParticipant.objects.get(
                session_id=session_id,
                user_id=user_id
            )
            participant.is_active = False
            participant.save(update_fields=['is_active'])

            return True
        except Exception as e:
            logger.exception(f"Error removing session participant: {str(e)}")
            return False

    @database_sync_to_async
    def create_comment(self, session_id, user_id, content, component_id=None, canvas_position=None, parent_id=None):
        """
        Create a new comment
        """
        try:
            from my_app.models import CollaborationSession, Comment

            session = CollaborationSession.objects.get(id=session_id)
            user = User.objects.get(id=user_id)

            comment = Comment.objects.create(
                session=session,
                author=user,
                content=content,
                component_id=component_id,
                canvas_position=canvas_position or {},
                parent_id=parent_id
            )

            return {
                'id': str(comment.id),
                'author': comment.author.username,
                'content': comment.content,
                'component_id': comment.component_id,
                'canvas_position': comment.canvas_position,
                'created_at': comment.created_at.isoformat(),
                'status': comment.status,
                'parent_id': str(comment.parent.id) if comment.parent else None,
            }
        except Exception as e:
            logger.exception(f"Error creating comment: {str(e)}")
            return None

    @database_sync_to_async
    def update_comment(self, comment_id, user_id, content):
        """
        Update an existing comment
        """
        try:
            from my_app.models import Comment

            comment = Comment.objects.get(id=comment_id, author_id=user_id)
            comment.content = content
            comment.save(update_fields=['content', 'updated_at'])

            return {
                'id': str(comment.id),
                'author': comment.author.username,
                'content': comment.content,
                'component_id': comment.component_id,
                'canvas_position': comment.canvas_position,
                'updated_at': comment.updated_at.isoformat(),
                'status': comment.status,
            }
        except Exception as e:
            logger.exception(f"Error updating comment: {str(e)}")
            return None

    @database_sync_to_async
    def resolve_comment(self, comment_id, user_id):
        """
        Resolve a comment
        """
        try:
            from my_app.models import Comment

            comment = Comment.objects.get(id=comment_id)
            user = User.objects.get(id=user_id)
            comment.resolve(user)

            return {
                'id': str(comment.id),
                'status': comment.status,
                'resolved_at': comment.resolved_at.isoformat(),
                'resolved_by': comment.resolved_by.username,
            }
        except Exception as e:
            logger.exception(f"Error resolving comment: {str(e)}")
            return None

    @database_sync_to_async
    def create_edit_operation(self, session_id, user_id, operation_type, target_id, operation_data):
        """
        Create a new edit operation
        """
        try:
            from my_app.models import CollaborationSession, EditOperation

            session = CollaborationSession.objects.get(id=session_id)
            user = User.objects.get(id=user_id)

            operation = EditOperation.objects.create(
                session=session,
                user=user,
                operation_type=operation_type,
                target_id=target_id,
                operation_data=operation_data,
                vector_clock={},  # TODO: Implement proper vector clock
                dependencies=[]
            )

            return {
                'id': str(operation.id),
                'user_id': operation.user.id,
                'username': operation.user.username,
                'operation_type': operation.operation_type,
                'target_id': operation.target_id,
                'operation_data': operation.operation_data,
                'created_at': operation.created_at.isoformat(),
            }
        except Exception as e:
            logger.exception(f"Error creating edit operation: {str(e)}")
            return None
