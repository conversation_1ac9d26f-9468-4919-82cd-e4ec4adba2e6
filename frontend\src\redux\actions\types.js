// WebSocket action types
export const WEBSOCKET_CONNECTED = 'WEBSOCKET_CONNECTED';
export const WEBSOCKET_DISCONNECTED = 'WEBSOCKET_DISCONNECTED';
export const WEBSOCKET_ERROR = 'WEBSOCKET_ERROR';

// New WebSocket action types
export const WS_CONNECT = 'WS_CONNECT';
export const WS_CONNECTED = 'WS_CONNECTED';
export const WS_DISCONNECT = 'WS_DISCONNECT';
export const WS_DISCONNECTED = 'WS_DISCONNECTED';
export const WS_MESSAGE = 'WS_MESSAGE';
export const WS_MESSAGE_RECEIVED = 'WS_MESSAGE_RECEIVED';
export const WS_SEND_MESSAGE = 'WS_SEND_MESSAGE';
export const WS_ERROR = 'WS_ERROR';
export const WS_RECONNECT = 'WS_RECONNECT';
export const WS_CLEAR_MESSAGES = 'WS_CLEAR_MESSAGES';

// App data action types
export const FETCH_APP_DATA_REQUEST = 'FETCH_APP_DATA_REQUEST';
export const FETCH_APP_DATA_SUCCESS = 'FETCH_APP_DATA_SUCCESS';
export const FETCH_APP_DATA_FAILURE = 'FETCH_APP_DATA_FAILURE';

export const SAVE_APP_DATA_REQUEST = 'SAVE_APP_DATA_REQUEST';
export const SAVE_APP_DATA_SUCCESS = 'SAVE_APP_DATA_SUCCESS';
export const SAVE_APP_DATA_FAILURE = 'SAVE_APP_DATA_FAILURE';

// Component action types
export const ADD_COMPONENT = 'ADD_COMPONENT';
export const UPDATE_COMPONENT = 'UPDATE_COMPONENT';
export const REMOVE_COMPONENT = 'REMOVE_COMPONENT';

// Layout action types
export const ADD_LAYOUT = 'ADD_LAYOUT';
export const UPDATE_LAYOUT = 'UPDATE_LAYOUT';
export const REMOVE_LAYOUT = 'REMOVE_LAYOUT';

// Style action types
export const ADD_STYLE = 'ADD_STYLE';
export const UPDATE_STYLE = 'UPDATE_STYLE';
export const REMOVE_STYLE = 'REMOVE_STYLE';

// Data action types
export const ADD_DATA = 'ADD_DATA';
export const UPDATE_DATA = 'UPDATE_DATA';
export const REMOVE_DATA = 'REMOVE_DATA';

// Theme action types
export const ADD_THEME = 'ADD_THEME';
export const UPDATE_THEME = 'UPDATE_THEME';
export const REMOVE_THEME = 'REMOVE_THEME';
export const SET_ACTIVE_THEME = 'SET_ACTIVE_THEME';
export const SAVE_USER_THEME_PREFERENCE = 'SAVE_USER_THEME_PREFERENCE';
export const TOGGLE_AUTO_APPLY_THEME = 'TOGGLE_AUTO_APPLY_THEME';

// API Keys action types
export const FETCH_API_KEYS_REQUEST = 'FETCH_API_KEYS_REQUEST';
export const FETCH_API_KEYS_SUCCESS = 'FETCH_API_KEYS_SUCCESS';
export const FETCH_API_KEYS_FAILURE = 'FETCH_API_KEYS_FAILURE';
export const VALIDATE_API_KEY_REQUEST = 'VALIDATE_API_KEY_REQUEST';
export const VALIDATE_API_KEY_SUCCESS = 'VALIDATE_API_KEY_SUCCESS';
export const VALIDATE_API_KEY_FAILURE = 'VALIDATE_API_KEY_FAILURE';

// AI action types
export const GENERATE_AI_SUGGESTIONS_REQUEST = 'GENERATE_AI_SUGGESTIONS_REQUEST';
export const GENERATE_AI_SUGGESTIONS_SUCCESS = 'GENERATE_AI_SUGGESTIONS_SUCCESS';
export const GENERATE_AI_SUGGESTIONS_FAILURE = 'GENERATE_AI_SUGGESTIONS_FAILURE';

export const GENERATE_IMAGE_REQUEST = 'GENERATE_IMAGE_REQUEST';
export const GENERATE_IMAGE_SUCCESS = 'GENERATE_IMAGE_SUCCESS';
export const GENERATE_IMAGE_FAILURE = 'GENERATE_IMAGE_FAILURE';

// AI Undo/Redo action types
export const AI_UNDO = 'AI_UNDO';
export const AI_REDO = 'AI_REDO';
export const AI_SAVE_STATE = 'AI_SAVE_STATE';
export const AI_CLEAR_HISTORY = 'AI_CLEAR_HISTORY';
export const AI_JUMP_TO_STATE = 'AI_JUMP_TO_STATE';
export const AI_RESTORE_STATE = 'AI_RESTORE_STATE';

// AI Layout and Component action types
export const APPLY_AI_LAYOUT = 'APPLY_AI_LAYOUT';
export const APPLY_AI_COMPONENT_COMBINATION = 'APPLY_AI_COMPONENT_COMBINATION';
export const AI_BULK_CHANGES = 'AI_BULK_CHANGES';
