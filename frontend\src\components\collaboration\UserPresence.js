import React, { useState, useEffect, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Card } from '../../design-system';
import { Avatar, Tooltip, Badge } from 'antd';
import { UserOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useCollaboration } from '../../contexts/CollaborationContext';
import styled from 'styled-components';
import theme from '../../design-system/theme';

const PresenceContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const UsersList = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${theme.spacing[2]};
`;

const UserItem = styled.div`
  display: flex;
  align-items: center;
  padding: ${theme.spacing[2]};
  border-radius: ${theme.borderRadius.md};
  background-color: ${theme.colors.neutral[100]};
  
  &:hover {
    background-color: ${theme.colors.neutral[200]};
  }
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: ${props => props.color || theme.colors.primary.main};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: ${theme.spacing[2]};
`;

const UserInfo = styled.div`
  flex: 1;
`;

const UserName = styled.div`
  font-weight: ${theme.typography.fontWeight.medium};
`;

const UserStatus = styled.div`
  display: flex;
  align-items: center;
  font-size: ${theme.typography.fontSize.xs};
  color: ${props => {
    switch (props.status) {
      case 'active': return theme.colors.success.main;
      case 'idle': return theme.colors.warning.main;
      case 'offline': return theme.colors.neutral[500];
      default: return theme.colors.neutral[500];
    }
  }};
`;

const StatusDot = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => {
    switch (props.status) {
      case 'active': return theme.colors.success.main;
      case 'idle': return theme.colors.warning.main;
      case 'offline': return theme.colors.neutral[500];
      default: return theme.colors.neutral[500];
    }
  }};
  margin-right: ${theme.spacing[1]};
`;

const LastSeen = styled.div`
  display: flex;
  align-items: center;
  font-size: ${theme.typography.fontSize.xs};
  color: ${theme.colors.neutral[500]};
  margin-left: ${theme.spacing[2]};
`;

// New components for real-time collaboration
const CursorIndicator = styled.div`
  position: absolute;
  width: 2px;
  height: 20px;
  background-color: ${props => props.color};
  pointer-events: none;
  z-index: 1000;
  transition: all 0.1s ease;

  &::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    width: 0;
    height: 0;
    border-left: 8px solid ${props => props.color};
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
  }
`;

const CursorLabel = styled.div`
  position: absolute;
  top: -25px;
  left: 10px;
  background-color: ${props => props.color};
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 1001;
`;

const SelectionHighlight = styled.div`
  position: absolute;
  border: 2px solid ${props => props.color};
  background-color: ${props => props.color}20;
  pointer-events: none;
  z-index: 999;
  border-radius: 4px;
  transition: all 0.2s ease;
`;

const CollaboratorsBar = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid ${theme.colors.neutral[200]};
`;

const StatusIndicator = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: ${props => {
    switch (props.status) {
      case 'active': return theme.colors.success.main;
      case 'idle': return theme.colors.warning.main;
      case 'away': return theme.colors.error.main;
      default: return theme.colors.neutral[400];
    }
  }};
  position: absolute;
  bottom: 0;
  right: 0;
  border: 2px solid white;
`;

// User cursor component
export const UserCursor = ({ userId, position, username, color }) => {
  const [visible, setVisible] = useState(true);
  const timeoutRef = useRef();

  useEffect(() => {
    setVisible(true);

    // Hide cursor after 3 seconds of inactivity
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setVisible(false);
    }, 3000);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [position]);

  if (!visible || !position) {
    return null;
  }

  return (
    <CursorIndicator
      color={color}
      style={{
        left: position.x,
        top: position.y,
        opacity: visible ? 1 : 0
      }}
    >
      <CursorLabel color={color}>
        {username}
      </CursorLabel>
    </CursorIndicator>
  );
};

// Component selection highlight
export const ComponentSelectionHighlight = ({ userId, selection, color }) => {
  if (!selection || !selection.bounds) {
    return null;
  }

  const { x, y, width, height } = selection.bounds;

  return (
    <SelectionHighlight
      color={color}
      style={{
        left: x,
        top: y,
        width,
        height
      }}
    />
  );
};

// Collaborator avatar with status
export const CollaboratorAvatar = ({
  user,
  color,
  status = 'active',
  showStatus = true,
  size = 'default'
}) => {
  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'Active';
      case 'idle': return 'Idle';
      case 'away': return 'Away';
      default: return 'Offline';
    }
  };

  return (
    <Tooltip title={`${user.username} - ${getStatusText(status)}`}>
      <div style={{ position: 'relative', display: 'inline-block' }}>
        <Avatar
          size={size}
          style={{
            backgroundColor: color,
            cursor: 'pointer'
          }}
          icon={<UserOutlined />}
        >
          {user.username?.charAt(0)?.toUpperCase()}
        </Avatar>
        {showStatus && (
          <StatusIndicator status={status} />
        )}
      </div>
    </Tooltip>
  );
};

// Compact collaborators bar
export const CollaboratorsBar = ({
  maxVisible = 5,
  showStatus = true,
  size = 'small'
}) => {
  const {
    activeParticipants,
    getParticipantColor,
    userPresence
  } = useCollaboration();

  const getParticipantStatus = (participant) => {
    const presence = userPresence[participant.user_id];
    if (!presence) return 'offline';

    const lastSeen = new Date(presence.timestamp || participant.last_seen);
    const now = new Date();
    const timeDiff = now - lastSeen;

    if (timeDiff < 30000) return 'active'; // 30 seconds
    if (timeDiff < 300000) return 'idle'; // 5 minutes
    return 'away';
  };

  const visibleParticipants = activeParticipants.slice(0, maxVisible);
  const hiddenCount = Math.max(0, activeParticipants.length - maxVisible);

  if (activeParticipants.length === 0) {
    return null;
  }

  return (
    <CollaboratorsBar>
      {visibleParticipants.map(participant => (
        <CollaboratorAvatar
          key={participant.user_id}
          user={{
            id: participant.user_id,
            username: participant.username
          }}
          color={getParticipantColor(participant.user_id)}
          status={getParticipantStatus(participant)}
          showStatus={showStatus}
          size={size}
        />
      ))}

      {hiddenCount > 0 && (
        <Tooltip title={`${hiddenCount} more collaborator${hiddenCount > 1 ? 's' : ''}`}>
          <Avatar size={size} style={{ backgroundColor: '#f0f0f0', color: '#666' }}>
            +{hiddenCount}
          </Avatar>
        </Tooltip>
      )}
    </CollaboratorsBar>
  );
};

// Presence overlay component for the canvas
export const PresenceOverlay = ({ containerRef }) => {
  const {
    activeParticipants,
    userPresence,
    getParticipantColor
  } = useCollaboration();

  return (
    <div style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, pointerEvents: 'none' }}>
      {activeParticipants.map(participant => {
        const presence = userPresence[participant.user_id];
        const color = getParticipantColor(participant.user_id);

        return (
          <React.Fragment key={participant.user_id}>
            {/* User cursor */}
            {presence?.cursor_position && (
              <UserCursor
                userId={participant.user_id}
                position={presence.cursor_position}
                username={participant.username}
                color={color}
              />
            )}

            {/* Selection highlight */}
            {presence?.selection && (
              <ComponentSelectionHighlight
                userId={participant.user_id}
                selection={presence.selection}
                color={color}
              />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

const UserPresence = () => {
  const {
    activeParticipants,
    getParticipantColor,
    userPresence,
    isConnected
  } = useCollaboration();
  const currentUser = useSelector(state => state.user?.username || 'User');

  const getParticipantStatus = (participant) => {
    const presence = userPresence[participant.user_id];
    if (!presence) return 'offline';

    const lastSeen = new Date(presence.timestamp || participant.last_seen);
    const now = new Date();
    const timeDiff = now - lastSeen;

    if (timeDiff < 30000) return 'active'; // 30 seconds
    if (timeDiff < 300000) return 'idle'; // 5 minutes
    return 'away';
  };

  const formatLastSeen = (timestamp) => {
    if (!timestamp) return 'Never';

    const now = Date.now();
    const diff = now - timestamp;

    if (diff < 60 * 1000) {
      return 'Just now';
    } else if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    } else if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
  };

  const formatLastSeen = (timestamp) => {
    if (!timestamp) return 'Never';

    const now = Date.now();
    const diff = now - timestamp;

    if (diff < 60 * 1000) {
      return 'Just now';
    } else if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    } else if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
  };

  // Sort participants: current user first, then by status
  const sortedParticipants = [...activeParticipants].sort((a, b) => {
    if (a.username === currentUser) return -1;
    if (b.username === currentUser) return 1;

    const statusA = getParticipantStatus(a);
    const statusB = getParticipantStatus(b);
    const statusOrder = { active: 0, idle: 1, away: 2, offline: 3 };
    return statusOrder[statusA] - statusOrder[statusB];
  });

  return (
    <Card title="Team Members" fullWidth>
      <PresenceContainer>
        <UsersList>
          {sortedParticipants.length === 0 ? (
            <div style={{ textAlign: 'center', color: theme.colors.neutral[500], padding: theme.spacing[4] }}>
              {isConnected ? 'No other collaborators online.' : 'Not connected to collaboration session.'}
            </div>
          ) : (
            sortedParticipants.map((participant) => {
              const status = getParticipantStatus(participant);
              const color = getParticipantColor(participant.user_id);

              return (
                <UserItem key={participant.user_id}>
                  <UserAvatar color={color}>
                    <UserOutlined />
                  </UserAvatar>
                  <UserInfo>
                    <UserName>
                      {participant.username}
                      {participant.username === currentUser && ' (You)'}
                    </UserName>
                    <UserStatus status={status}>
                      <StatusDot status={status} />
                      {status === 'active' ? 'Online' :
                        status === 'idle' ? 'Idle' :
                          status === 'away' ? 'Away' : 'Offline'}
                    </UserStatus>
                  </UserInfo>
                  {status !== 'active' && (
                    <LastSeen>
                      <ClockCircleOutlined style={{ marginRight: '4px' }} />
                      {formatLastSeen(new Date(participant.last_seen).getTime())}
                    </LastSeen>
                  )}
                </UserItem>
              );
            })
          )}
        </UsersList>
      </PresenceContainer>
    </Card>
  );
};

// Hook for tracking mouse position and sending presence updates
export const usePresenceTracking = (containerRef) => {
  const { updatePresence, isConnected } = useCollaboration();
  const [selectedComponent, setSelectedComponent] = useState(null);

  useEffect(() => {
    if (!containerRef.current || !isConnected) return;

    const container = containerRef.current;
    let mousePosition = { x: 0, y: 0 };
    let updateTimeout;

    const handleMouseMove = (event) => {
      const rect = container.getBoundingClientRect();
      mousePosition = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };

      // Debounce presence updates
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }

      updateTimeout = setTimeout(() => {
        updatePresence({
          cursor_position: mousePosition
        });
      }, 100);
    };

    const handleMouseLeave = () => {
      updatePresence({
        cursor_position: null
      });
    };

    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }
    };
  }, [containerRef, isConnected, updatePresence]);

  const selectComponent = (componentId, bounds = null) => {
    setSelectedComponent(componentId);
    updatePresence({
      selection: {
        component_id: componentId,
        bounds
      }
    });
  };

  const clearSelection = () => {
    setSelectedComponent(null);
    updatePresence({
      selection: null
    });
  };

  return {
    selectedComponent,
    selectComponent,
    clearSelection
  };
};

export default UserPresence;
