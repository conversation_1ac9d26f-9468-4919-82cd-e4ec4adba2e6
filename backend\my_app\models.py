from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import json
import uuid

class App(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='apps', null=True, blank=True)
    app_data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_public = models.BooleanField(default=False)

    # Collaboration settings
    allow_collaboration = models.BooleanField(default=True)
    collaboration_mode = models.CharField(
        max_length=20,
        choices=[
            ('private', 'Private'),
            ('invite_only', 'Invite Only'),
            ('public', 'Public'),
        ],
        default='private'
    )

    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['created_at']),
            models.Index(fields=['updated_at']),
        ]

    def __str__(self):
        return f"{self.name} ({self.user.username if self.user else 'Anonymous'})"

    def get_app_data_json(self):
        """Return app_data as a Python dictionary"""
        try:
            return json.loads(self.app_data)
        except (json.JSONDecodeError, TypeError):
            return {"components": [], "layouts": [], "styles": {}, "data": {}}

    def create_version(self, commit_message, created_by):
        version_number = self.versions.count() + 1
        return AppVersion.objects.create(
            app=self,
            version_number=version_number,
            app_data=self.app_data,
            created_by=created_by,
            commit_message=commit_message
        )

    def get_active_collaboration_session(self):
        """Get the currently active collaboration session for this app"""
        return self.collaboration_sessions.filter(is_active=True).first()

    def create_collaboration_session(self, created_by, name=None):
        """Create a new collaboration session for this app"""
        if not name:
            name = f"Collaboration on {self.name}"

        return CollaborationSession.objects.create(
            app=self,
            name=name,
            created_by=created_by
        )

class AppVersion(models.Model):
    app = models.ForeignKey(App, on_delete=models.CASCADE, related_name='versions')
    version_number = models.PositiveIntegerField()
    app_data = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    commit_message = models.TextField(blank=True)

    class Meta:
        unique_together = ('app', 'version_number')
        ordering = ['-version_number']
        indexes = [
            models.Index(fields=['app', 'version_number']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.app.name} v{self.version_number}"


class CollaborationSession(models.Model):
    """
    Model for managing collaborative editing sessions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    app = models.ForeignKey(App, on_delete=models.CASCADE, related_name='collaboration_sessions')
    name = models.CharField(max_length=255, default="Collaboration Session")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_sessions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    max_participants = models.PositiveIntegerField(default=10)

    # Session settings
    allow_anonymous = models.BooleanField(default=False)
    require_approval = models.BooleanField(default=False)

    class Meta:
        ordering = ['-updated_at']
        indexes = [
            models.Index(fields=['app']),
            models.Index(fields=['created_by']),
            models.Index(fields=['is_active']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Session: {self.name} ({self.app.name})"

    @property
    def active_participants_count(self):
        """Get count of currently active participants"""
        return self.participants.filter(is_active=True).count()


class SessionParticipant(models.Model):
    """
    Model for tracking users participating in collaboration sessions
    """
    ROLE_CHOICES = [
        ('owner', 'Owner'),
        ('editor', 'Editor'),
        ('viewer', 'Viewer'),
        ('commenter', 'Commenter'),
    ]

    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='collaboration_sessions')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='editor')
    joined_at = models.DateTimeField(auto_now_add=True)
    last_seen = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    # Presence data
    cursor_position = models.JSONField(default=dict, blank=True)
    selected_component_id = models.CharField(max_length=255, blank=True, null=True)
    current_view = models.CharField(max_length=100, blank=True, null=True)

    class Meta:
        unique_together = ('session', 'user')
        ordering = ['-last_seen']
        indexes = [
            models.Index(fields=['session', 'is_active']),
            models.Index(fields=['user']),
            models.Index(fields=['last_seen']),
        ]

    def __str__(self):
        return f"{self.user.username} in {self.session.name} ({self.role})"

    def update_presence(self, cursor_position=None, selected_component_id=None, current_view=None):
        """Update user presence information"""
        if cursor_position is not None:
            self.cursor_position = cursor_position
        if selected_component_id is not None:
            self.selected_component_id = selected_component_id
        if current_view is not None:
            self.current_view = current_view
        self.last_seen = timezone.now()
        self.save(update_fields=['cursor_position', 'selected_component_id', 'current_view', 'last_seen'])


class Comment(models.Model):
    """
    Model for contextual comments on app components and canvas areas
    """
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('resolved', 'Resolved'),
        ('archived', 'Archived'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')

    # Comment content
    content = models.TextField()
    content_type = models.CharField(max_length=20, default='text')  # text, html, markdown

    # Context information
    component_id = models.CharField(max_length=255, blank=True, null=True)
    canvas_position = models.JSONField(default=dict, blank=True)  # {x, y, width, height}
    context_data = models.JSONField(default=dict, blank=True)  # Additional context

    # Status and metadata
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_comments')

    # Mentions
    mentioned_users = models.ManyToManyField(User, blank=True, related_name='mentioned_in_comments')

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['session', 'status']),
            models.Index(fields=['author']),
            models.Index(fields=['component_id']),
            models.Index(fields=['parent']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"Comment by {self.author.username} on {self.session.name}"

    @property
    def is_thread_root(self):
        """Check if this comment is the root of a thread"""
        return self.parent is None

    @property
    def reply_count(self):
        """Get count of replies to this comment"""
        return self.replies.count()

    def resolve(self, resolved_by_user):
        """Mark comment as resolved"""
        self.status = 'resolved'
        self.resolved_at = timezone.now()
        self.resolved_by = resolved_by_user
        self.save(update_fields=['status', 'resolved_at', 'resolved_by'])

    def reopen(self):
        """Reopen a resolved comment"""
        self.status = 'open'
        self.resolved_at = None
        self.resolved_by = None
        self.save(update_fields=['status', 'resolved_at', 'resolved_by'])


class EditOperation(models.Model):
    """
    Model for tracking real-time editing operations for operational transformation
    """
    OPERATION_TYPES = [
        ('component_add', 'Add Component'),
        ('component_update', 'Update Component'),
        ('component_delete', 'Delete Component'),
        ('component_move', 'Move Component'),
        ('layout_update', 'Update Layout'),
        ('style_update', 'Update Style'),
        ('data_update', 'Update Data'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='edit_operations')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='edit_operations')

    # Operation details
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES)
    target_id = models.CharField(max_length=255)  # ID of the component/element being modified
    operation_data = models.JSONField()  # The actual operation data

    # Operational transformation data
    vector_clock = models.JSONField(default=dict)  # For ordering operations
    dependencies = models.JSONField(default=list)  # Operations this depends on

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    applied_at = models.DateTimeField(null=True, blank=True)
    is_applied = models.BooleanField(default=False)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['session', 'created_at']),
            models.Index(fields=['user']),
            models.Index(fields=['target_id']),
            models.Index(fields=['operation_type']),
            models.Index(fields=['is_applied']),
        ]

    def __str__(self):
        return f"{self.operation_type} by {self.user.username} on {self.target_id}"

    def apply_operation(self):
        """Mark operation as applied"""
        self.is_applied = True
        self.applied_at = timezone.now()
        self.save(update_fields=['is_applied', 'applied_at'])


class UserActivity(models.Model):
    """
    Model for tracking user activity and analytics in collaboration sessions
    """
    ACTIVITY_TYPES = [
        ('join', 'Joined Session'),
        ('leave', 'Left Session'),
        ('edit', 'Made Edit'),
        ('comment', 'Added Comment'),
        ('cursor_move', 'Moved Cursor'),
        ('component_select', 'Selected Component'),
        ('view_change', 'Changed View'),
    ]

    session = models.ForeignKey(CollaborationSession, on_delete=models.CASCADE, related_name='activities')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='collaboration_activities')
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES)
    activity_data = models.JSONField(default=dict, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['session', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['activity_type']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.activity_type} at {self.timestamp}"

    def get_app_data_json(self):
        """Return app_data as a Python dictionary"""
        try:
            return json.loads(self.app_data)
        except (json.JSONDecodeError, TypeError):
            return {"components": [], "layouts": [], "styles": {}, "data": {}}

class ComponentTemplate(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    component_type = models.CharField(max_length=100)
    default_props = models.TextField()
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='component_templates', null=True, blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['component_type']),
        ]

    def __str__(self):
        return self.name

    def get_default_props_json(self):
        """Return default_props as a Python dictionary"""
        try:
            return json.loads(self.default_props)
        except (json.JSONDecodeError, TypeError):
            return {}

class LayoutTemplate(models.Model):
    """
    Model for layout templates that define reusable layout structures.
    """
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    layout_type = models.CharField(max_length=100, help_text="Type of layout (grid, flex, sidebar, etc.)")
    components = models.JSONField(default=dict, help_text="Component references and configurations")
    default_props = models.JSONField(default=dict, help_text="Default properties for the layout")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='layout_templates', null=True, blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['layout_type']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_components_json(self):
        """Return components as a Python dictionary"""
        return self.components if isinstance(self.components, dict) else {}

    def get_default_props_json(self):
        """Return default_props as a Python dictionary"""
        return self.default_props if isinstance(self.default_props, dict) else {}


class AppTemplate(models.Model):
    """
    Model for complete application templates that serve as starting points.
    """
    APP_CATEGORIES = [
        ('business', 'Business Apps'),
        ('ecommerce', 'E-commerce'),
        ('portfolio', 'Portfolio'),
        ('dashboard', 'Dashboard'),
        ('landing', 'Landing Page'),
        ('blog', 'Blog'),
        ('social', 'Social Media'),
        ('education', 'Education'),
        ('healthcare', 'Healthcare'),
        ('finance', 'Finance'),
        ('other', 'Other'),
    ]

    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    app_category = models.CharField(max_length=100, choices=APP_CATEGORIES, default='other')
    components = models.JSONField(default=dict, help_text="Complete app structure with components")
    default_props = models.JSONField(default=dict, help_text="Default properties for the app")
    required_components = models.JSONField(default=list, help_text="List of required component dependencies")
    preview_image = models.URLField(blank=True, help_text="URL to template preview image")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='app_templates', null=True, blank=True)
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['is_public']),
            models.Index(fields=['app_category']),
            models.Index(fields=['created_at']),
        ]
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_components_json(self):
        """Return components as a Python dictionary"""
        return self.components if isinstance(self.components, dict) else {}

    def get_default_props_json(self):
        """Return default_props as a Python dictionary"""
        return self.default_props if isinstance(self.default_props, dict) else {}

    def get_required_components_list(self):
        """Return required_components as a Python list"""
        return self.required_components if isinstance(self.required_components, list) else []
