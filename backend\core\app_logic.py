"""
Core application logic for App Builder 201.
This module contains the main classes and functions for building applications.
"""

import openai
from .config import get_api_config

class PluginManager:
    """
    Manages plugins for the application builder.
    """
    def __init__(self):
        self.plugins = {}

    def register_plugin(self, name, plugin):
        """
        Register a plugin with the manager.

        Args:
            name (str): The name of the plugin
            plugin (object): The plugin instance
        """
        self.plugins[name] = plugin

    def get_plugin(self, name):
        """
        Get a plugin by name.

        Args:
            name (str): The name of the plugin

        Returns:
            object: The plugin instance or None if not found
        """
        return self.plugins.get(name)


class AppBuilder:
    """
    Main class for building applications.
    """
    def __init__(self):
        self.components = []
        self.layouts = []
        self.styles = {}
        self.data = {}
        self.plugin_manager = PluginManager()

    def add_component(self, component_type, props={}):
        """
        Add a component to the application.

        Args:
            component_type (str): The type of component
            props (dict): The properties of the component
        """
        self.components.append({"type": component_type, "props": props})

    def add_layout(self, layout_type, components=[], styles={}):
        """
        Add a layout to the application.

        Args:
            layout_type (str): The type of layout
            components (list): The components in the layout
            styles (dict): The styles for the layout
        """
        self.layouts.append({"type": layout_type, "components": components, "styles": styles})

    def add_style(self, selector, style):
        """
        Add a style to the application.

        Args:
            selector (str): The CSS selector
            style (dict): The style properties
        """
        self.styles[selector] = style

    def add_data(self, key, value):
        """
        Add data to the application.

        Args:
            key (str): The data key
            value (any): The data value
        """
        self.data[key] = value

    def build(self):
        """
        Build the application structure.

        Returns:
            dict: The application structure
        """
        return {
            "components": [component for component in self.components],
            "layouts": [layout for layout in self.layouts],
            "styles": {selector: style for selector, style in self.styles.items()},
            "data": {key: value for key, value in self.data.items()},
        }

    def export(self, format='web', options=None):
        """
        Export the application in the specified format using enhanced code generator.

        Args:
            format (str): The export format ('web', 'mobile', 'react', 'vue', etc.)
            options (dict): Export options for enhanced generation

        Returns:
            str or dict: The exported application

        Raises:
            ValueError: If the format is invalid
        """
        # Import enhanced code generator
        try:
            from .enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework

            # Map legacy formats to new formats
            format_mapping = {
                'web': ExportFormat.REACT,
                'mobile': ExportFormat.REACT_NATIVE,
                'json': 'json'
            }

            if format == 'json':
                return self.build()

            # Use enhanced code generator for modern formats
            if format in format_mapping or hasattr(ExportFormat, format.upper().replace('-', '_')):
                generator = EnhancedCodeGenerator()

                # Create export options
                export_options = ExportOptions(
                    format=format_mapping.get(format, getattr(ExportFormat, format.upper().replace('-', '_'), ExportFormat.REACT)),
                    typescript=options.get('typescript', False) if options else False,
                    include_accessibility=options.get('include_accessibility', True) if options else True,
                    include_tests=options.get('include_tests', False) if options else False,
                    style_framework=getattr(StyleFramework, options.get('style_framework', 'STYLED_COMPONENTS').upper()) if options else StyleFramework.STYLED_COMPONENTS,
                    project_structure=options.get('project_structure', 'single-file') if options else 'single-file',
                    include_docker=options.get('include_docker', False) if options else False,
                    include_ci_cd=options.get('include_ci_cd', False) if options else False
                )

                return generator.generate_code(self.build(), export_options)

            # Fallback to legacy generation for backward compatibility
            elif format == 'web':
                return self._generate_web_code(self.build())
            elif format == 'mobile':
                return self._generate_mobile_code(self.build())
            else:
                raise ValueError(f"Invalid export format: {format}")

        except ImportError:
            # Fallback to legacy generation if enhanced generator is not available
            if format == 'web':
                return self._generate_web_code(self.build())
            elif format == 'mobile':
                return self._generate_mobile_code(self.build())
            elif format == 'json':
                return self.build()
            else:
                raise ValueError("Invalid export format")

    def _generate_web_code(self, app_structure):
        """
        Generate web code from the application structure.

        Args:
            app_structure (dict): The application structure

        Returns:
            str: The generated web code
        """
        # Implement HTML/React code generation
        html = "<!DOCTYPE html>\n<html>\n<head>\n  <title>Generated App</title>\n"

        # Add styles
        html += "  <style>\n"
        for selector, style in app_structure["styles"].items():
            html += f"    {selector} {{\n"
            for prop, value in style.items():
                html += f"      {prop}: {value};\n"
            html += "    }\n"
        html += "  </style>\n</head>\n<body>\n"

        # Add components
        html += "  <div id='app'>\n"
        for layout in app_structure["layouts"]:
            html += f"    <div class='{layout['type']}'>\n"
            for component in layout["components"]:
                component_data = next((c for c in app_structure["components"] if c["type"] == component), None)
                if component_data:
                    html += f"      <div class='{component_data['type']}'>\n"
                    for prop, value in component_data["props"].items():
                        if prop == "text":
                            html += f"        {value}\n"
                    html += "      </div>\n"
            html += "    </div>\n"
        html += "  </div>\n"

        # Add data
        html += "  <script>\n    const appData = " + str(app_structure["data"]) + ";\n  </script>\n"

        html += "</body>\n</html>"
        return html

    def _generate_mobile_code(self, app_structure):
        """
        Generate mobile code from the application structure.

        Args:
            app_structure (dict): The application structure

        Returns:
            str: The generated mobile code
        """
        # Implement React Native code generation
        code = "import React from 'react';\n"
        code += "import { View, Text, StyleSheet } from 'react-native';\n\n"

        # Generate component imports
        for component in app_structure["components"]:
            code += f"import {component['type']} from './components/{component['type']}';\n"

        code += "\nconst App = () => {\n"
        code += "  return (\n    <View style={styles.container}>\n"

        # Add layouts
        for layout in app_structure["layouts"]:
            code += f"      <View style={{{{styles.{layout['type']}}}}}>\n"
            for component in layout["components"]:
                component_data = next((c for c in app_structure["components"] if c["type"] == component), None)
                if component_data:
                    props = ", ".join([f"{k}: '{v}'" for k, v in component_data["props"].items()])
                    code += f"        <{component_data['type']} {{{{{props}}}}} />\n"
            code += "      </View>\n"

        code += "    </View>\n  );\n};\n\n"

        # Add styles
        code += "const styles = StyleSheet.create({\n"
        code += "  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n"
        for selector, style in app_structure["styles"].items():
            code += f"  {selector}: {{\n"
            for prop, value in style.items():
                code += f"    {prop}: '{value}',\n"
            code += "  },\n"
        code += "});\n\n"

        code += "export default App;"
        return code

    def register_plugin(self, plugin):
        """
        Register a plugin with the application builder.

        Args:
            plugin (object): The plugin instance
        """
        self.plugin_manager.register_plugin(plugin.__class__.__name__, plugin)

    def use_plugin(self, plugin_name, *args, **kwargs):
        """
        Use a plugin.

        Args:
            plugin_name (str): The name of the plugin
            *args: Arguments to pass to the plugin
            **kwargs: Keyword arguments to pass to the plugin

        Returns:
            any: The result of the plugin

        Raises:
            ValueError: If the plugin is not found
        """
        plugin = self.plugin_manager.get_plugin(plugin_name)
        if plugin:
            return plugin(*args, **kwargs)
        else:
            raise ValueError(f"Plugin '{plugin_name}' not found")


class AIPlugin:
    """
    Plugin for AI-powered features.
    """
    def __init__(self, api_key=None):
        config = get_api_config()
        self.openai_api_key = api_key or config.openai_api_key
        self.ai_studio_api_key = config.ai_studio_api_key
        self.stability_api_key = config.stability_api_key
        self.elevenlabs_api_key = config.elevenlabs_api_key

        # Set OpenAI API key
        openai.api_key = self.openai_api_key

    def get_suggestions(self, prompt):
        try:
            response = openai.Completion.create(
                engine="davinci",
                prompt=prompt,
                max_tokens=100
            )
            return response.choices[0].text.strip()
        except Exception as e:
            print(f"OpenAI API error: {str(e)}")
            return "Error generating suggestions"

    def generate_image(self, prompt, api='openai'):
        """
        Generate an image based on a prompt.

        Args:
            prompt (str): The prompt for the image generation
            api (str): The API to use ('openai', 'stability', or 'placeholder')

        Returns:
            str: The URL of the generated image
        """
        try:
            if api == 'openai' and self.openai_api_key:
                # Use OpenAI's DALL-E API
                response = openai.Image.create(
                    prompt=prompt,
                    n=1,
                    size="1024x1024"
                )
                return response['data'][0]['url']

            elif api == 'stability' and self.stability_api_key:
                # Use Stability AI's API
                # This is a placeholder for the actual implementation
                # In a real implementation, you would use the Stability AI SDK
                return f"https://via.placeholder.com/1024x1024?text=Stability+AI+Image:{prompt}"

            else:
                # Fallback to placeholder
                return f"https://via.placeholder.com/300x200?text=AI+Generated+Image:{prompt.replace(' ', '+')}"
        except Exception as e:
            print(f"Error generating image: {str(e)}")
            return f"https://via.placeholder.com/300x200?text=Error+Generating+Image"

    def __call__(self, *args, **kwargs):
        """
        Make the plugin callable.

        Args:
            *args: Arguments
            **kwargs: Keyword arguments

        Returns:
            any: The result of the plugin
        """
        method = kwargs.get('method', 'get_suggestions')
        if method == 'get_suggestions':
            return self.get_suggestions(kwargs.get('prompt', ''))
        elif method == 'generate_image':
            return self.generate_image(kwargs.get('prompt', ''))
        else:
            raise ValueError(f"Unknown method: {method}")


class AILayoutSuggestionsEngine:
    """
    AI-powered layout suggestions engine for App Builder.
    Analyzes current app structure and suggests optimal layout patterns.
    """

    def __init__(self, api_key=None):
        config = get_api_config()
        self.openai_api_key = api_key or config.openai_api_key
        openai.api_key = self.openai_api_key

        # Layout pattern definitions
        self.layout_patterns = {
            'header_footer': {
                'name': 'Header-Footer Layout',
                'description': 'Classic layout with header, main content, and footer',
                'use_cases': ['landing_page', 'blog', 'corporate_site'],
                'structure': {
                    'header': {'height': '60px', 'position': 'top'},
                    'main': {'flex': '1', 'position': 'center'},
                    'footer': {'height': '40px', 'position': 'bottom'}
                }
            },
            'sidebar_main': {
                'name': 'Sidebar Layout',
                'description': 'Layout with sidebar navigation and main content area',
                'use_cases': ['dashboard', 'admin_panel', 'documentation'],
                'structure': {
                    'sidebar': {'width': '250px', 'position': 'left'},
                    'main': {'flex': '1', 'position': 'right'}
                }
            },
            'grid_layout': {
                'name': 'Grid Layout',
                'description': 'Responsive grid system for organizing content',
                'use_cases': ['portfolio', 'gallery', 'product_catalog'],
                'structure': {
                    'container': {'display': 'grid', 'gap': '16px'},
                    'items': {'grid-template-columns': 'repeat(auto-fit, minmax(300px, 1fr))'}
                }
            },
            'flexbox_layout': {
                'name': 'Flexbox Layout',
                'description': 'Flexible layout using CSS flexbox',
                'use_cases': ['form', 'card_layout', 'navigation'],
                'structure': {
                    'container': {'display': 'flex', 'flex-direction': 'column'},
                    'items': {'flex': '1'}
                }
            },
            'hero_section': {
                'name': 'Hero Section Layout',
                'description': 'Large hero section with call-to-action',
                'use_cases': ['landing_page', 'marketing_site', 'product_launch'],
                'structure': {
                    'hero': {'height': '100vh', 'display': 'flex', 'align-items': 'center'},
                    'content': {'text-align': 'center', 'max-width': '800px'}
                }
            }
        }

    def analyze_app_structure(self, components, layouts=None):
        """
        Analyze the current app structure to understand the context.

        Args:
            components (list): List of components in the app
            layouts (list): List of existing layouts

        Returns:
            dict: Analysis results with component types, patterns, and context
        """
        analysis = {
            'component_count': len(components),
            'component_types': {},
            'has_navigation': False,
            'has_forms': False,
            'has_media': False,
            'complexity_score': 0,
            'app_type': 'unknown'
        }

        # Analyze component types
        for component in components:
            comp_type = component.get('type', 'unknown')
            analysis['component_types'][comp_type] = analysis['component_types'].get(comp_type, 0) + 1

            # Check for specific patterns
            if comp_type in ['header', 'nav', 'menu']:
                analysis['has_navigation'] = True
            elif comp_type in ['form', 'input', 'button']:
                analysis['has_forms'] = True
            elif comp_type in ['image', 'video', 'gallery']:
                analysis['has_media'] = True

        # Calculate complexity score
        analysis['complexity_score'] = len(analysis['component_types']) * 2 + analysis['component_count']

        # Determine app type based on components
        analysis['app_type'] = self._determine_app_type(analysis)

        return analysis

    def _determine_app_type(self, analysis):
        """Determine the type of app based on component analysis."""
        component_types = analysis['component_types']

        if 'form' in component_types and component_types['form'] > 2:
            return 'form_heavy'
        elif analysis['has_media'] and 'card' in component_types:
            return 'portfolio'
        elif analysis['has_navigation'] and 'dashboard' in component_types:
            return 'dashboard'
        elif 'button' in component_types and 'text' in component_types:
            return 'landing_page'
        else:
            return 'general'

    def suggest_layouts(self, components, layouts=None, context=None):
        """
        Generate layout suggestions based on app structure analysis.

        Args:
            components (list): Current components in the app
            layouts (list): Existing layouts
            context (dict): Additional context (user preferences, app type, etc.)

        Returns:
            list: List of layout suggestions with scores and explanations
        """
        analysis = self.analyze_app_structure(components, layouts)
        suggestions = []

        # Get base suggestions based on app type
        base_patterns = self._get_patterns_for_app_type(analysis['app_type'])

        for pattern_key in base_patterns:
            pattern = self.layout_patterns[pattern_key]
            score = self._calculate_pattern_score(pattern, analysis, context)

            suggestion = {
                'id': pattern_key,
                'name': pattern['name'],
                'description': pattern['description'],
                'score': score,
                'structure': pattern['structure'],
                'explanation': self._generate_explanation(pattern, analysis),
                'preview_data': self._generate_preview_data(pattern, components),
                'applicable_components': self._get_applicable_components(pattern, components)
            }
            suggestions.append(suggestion)

        # Sort by score (highest first)
        suggestions.sort(key=lambda x: x['score'], reverse=True)

        # Add AI-enhanced suggestions if API is available
        if self.openai_api_key:
            ai_suggestions = self._get_ai_enhanced_suggestions(analysis, components)
            suggestions.extend(ai_suggestions)

        return suggestions[:5]  # Return top 5 suggestions

    def _get_patterns_for_app_type(self, app_type):
        """Get recommended layout patterns for a specific app type."""
        pattern_mapping = {
            'dashboard': ['sidebar_main', 'grid_layout', 'flexbox_layout'],
            'landing_page': ['hero_section', 'header_footer', 'flexbox_layout'],
            'portfolio': ['grid_layout', 'header_footer', 'flexbox_layout'],
            'form_heavy': ['flexbox_layout', 'sidebar_main', 'header_footer'],
            'general': ['header_footer', 'flexbox_layout', 'grid_layout']
        }
        return pattern_mapping.get(app_type, pattern_mapping['general'])

    def _calculate_pattern_score(self, pattern, analysis, context=None):
        """Calculate a score for how well a pattern fits the current app."""
        score = 50  # Base score

        # Adjust based on component count
        if analysis['component_count'] > 10 and pattern['name'] == 'Grid Layout':
            score += 20
        elif analysis['component_count'] < 5 and pattern['name'] == 'Hero Section Layout':
            score += 15

        # Adjust based on component types
        if analysis['has_navigation'] and 'sidebar' in pattern['structure']:
            score += 25
        if analysis['has_forms'] and pattern['name'] == 'Flexbox Layout':
            score += 20
        if analysis['has_media'] and pattern['name'] == 'Grid Layout':
            score += 30

        # Context-based adjustments
        if context:
            if context.get('mobile_first') and pattern['name'] in ['Flexbox Layout', 'Grid Layout']:
                score += 15
            if context.get('accessibility_focus') and pattern['name'] == 'Header-Footer Layout':
                score += 10

        return min(score, 100)  # Cap at 100

    def _generate_explanation(self, pattern, analysis):
        """Generate an explanation for why this pattern is recommended."""
        explanations = []

        if analysis['component_count'] > 8 and 'grid' in pattern['name'].lower():
            explanations.append("Grid layout works well for organizing many components")

        if analysis['has_navigation'] and 'sidebar' in pattern['structure']:
            explanations.append("Sidebar layout provides easy navigation access")

        if analysis['has_forms'] and 'flex' in pattern['name'].lower():
            explanations.append("Flexbox layout offers excellent form organization")

        if not explanations:
            explanations.append(f"This {pattern['name'].lower()} is suitable for your app structure")

        return ". ".join(explanations) + "."

    def _generate_preview_data(self, pattern, components):
        """Generate preview data for the layout pattern."""
        return {
            'css_classes': f"layout-{pattern['name'].lower().replace(' ', '-')}",
            'estimated_components': min(len(components), 6),
            'responsive': True,
            'mobile_friendly': pattern['name'] in ['Flexbox Layout', 'Grid Layout']
        }

    def _get_applicable_components(self, pattern, components):
        """Get list of components that work well with this pattern."""
        applicable = []

        for component in components:
            comp_type = component.get('type', '')
            if pattern['name'] == 'Grid Layout' and comp_type in ['card', 'image', 'text']:
                applicable.append(component.get('id'))
            elif pattern['name'] == 'Sidebar Layout' and comp_type in ['nav', 'menu', 'list']:
                applicable.append(component.get('id'))
            elif pattern['name'] == 'Hero Section Layout' and comp_type in ['text', 'button', 'image']:
                applicable.append(component.get('id'))

        return applicable

    def _get_ai_enhanced_suggestions(self, analysis, components):
        """Get AI-enhanced layout suggestions using OpenAI."""
        try:
            # Create a prompt for AI analysis
            prompt = f"""
            Analyze this app structure and suggest optimal layouts:

            Component Count: {analysis['component_count']}
            Component Types: {', '.join(analysis['component_types'].keys())}
            App Type: {analysis['app_type']}
            Has Navigation: {analysis['has_navigation']}
            Has Forms: {analysis['has_forms']}
            Has Media: {analysis['has_media']}

            Suggest 2 additional layout patterns that would work well for this app.
            Format as JSON with: name, description, reasoning, css_properties
            """

            response = openai.Completion.create(
                engine="text-davinci-003",
                prompt=prompt,
                max_tokens=300,
                temperature=0.7
            )

            # Parse AI response (simplified - in production, would need better parsing)
            ai_text = response.choices[0].text.strip()

            # Create AI suggestion (fallback format)
            ai_suggestion = {
                'id': 'ai_custom',
                'name': 'AI Custom Layout',
                'description': 'AI-generated layout based on your app structure',
                'score': 85,
                'structure': {'ai_generated': True},
                'explanation': ai_text[:200] + "..." if len(ai_text) > 200 else ai_text,
                'preview_data': {'ai_enhanced': True},
                'applicable_components': [comp.get('id') for comp in components[:3]]
            }

            return [ai_suggestion]

        except Exception as e:
            print(f"AI enhancement error: {str(e)}")
            return []


class AIComponentCombinationEngine:
    """
    AI-powered component combination recommendations engine.
    Suggests complementary component combinations based on UI patterns.
    """

    def __init__(self, api_key=None):
        config = get_api_config()
        self.openai_api_key = api_key or config.openai_api_key
        openai.api_key = self.openai_api_key

        # Common component combination patterns
        self.combination_patterns = {
            'form_pattern': {
                'name': 'Form with Validation',
                'components': ['form', 'input', 'button', 'text'],
                'description': 'Complete form setup with validation and submit button',
                'use_cases': ['contact_form', 'registration', 'survey']
            },
            'card_action_pattern': {
                'name': 'Card with Actions',
                'components': ['card', 'text', 'button', 'image'],
                'description': 'Interactive card with content and action buttons',
                'use_cases': ['product_card', 'profile_card', 'article_preview']
            },
            'navigation_pattern': {
                'name': 'Navigation Menu',
                'components': ['header', 'nav', 'list', 'button'],
                'description': 'Complete navigation setup with menu items',
                'use_cases': ['main_navigation', 'sidebar_menu', 'breadcrumbs']
            },
            'hero_cta_pattern': {
                'name': 'Hero with Call-to-Action',
                'components': ['section', 'text', 'button', 'image'],
                'description': 'Hero section with compelling text and action button',
                'use_cases': ['landing_page', 'product_launch', 'marketing']
            },
            'data_display_pattern': {
                'name': 'Data Display Grid',
                'components': ['card', 'text', 'list', 'divider'],
                'description': 'Organized data display with clear separation',
                'use_cases': ['dashboard', 'analytics', 'reports']
            }
        }

    def suggest_combinations(self, current_components, selected_component=None, context=None):
        """
        Suggest component combinations based on current app state.

        Args:
            current_components (list): Current components in the app
            selected_component (dict): Currently selected component
            context (dict): Additional context for suggestions

        Returns:
            list: List of component combination suggestions
        """
        suggestions = []

        # Analyze current components
        component_types = [comp.get('type') for comp in current_components]

        # Get pattern-based suggestions
        for pattern_key, pattern in self.combination_patterns.items():
            score = self._calculate_combination_score(pattern, component_types, selected_component)

            if score > 30:  # Only suggest if score is reasonable
                suggestion = {
                    'id': pattern_key,
                    'name': pattern['name'],
                    'description': pattern['description'],
                    'components': pattern['components'],
                    'score': score,
                    'explanation': self._generate_combination_explanation(pattern, component_types),
                    'missing_components': self._get_missing_components(pattern, component_types),
                    'use_cases': pattern['use_cases']
                }
                suggestions.append(suggestion)

        # Add context-based suggestions
        if selected_component:
            contextual_suggestions = self._get_contextual_suggestions(selected_component, current_components)
            suggestions.extend(contextual_suggestions)

        # Sort by score
        suggestions.sort(key=lambda x: x['score'], reverse=True)

        return suggestions[:4]  # Return top 4 suggestions

    def _calculate_combination_score(self, pattern, current_types, selected_component):
        """Calculate how well a combination pattern fits the current state."""
        score = 40  # Base score

        # Check how many pattern components are already present
        pattern_components = set(pattern['components'])
        current_components = set(current_types)

        overlap = len(pattern_components.intersection(current_components))
        missing = len(pattern_components - current_components)

        # Higher score if some components exist but pattern is not complete
        if overlap > 0 and missing > 0:
            score += overlap * 15
            score -= missing * 5

        # Bonus if selected component is part of the pattern
        if selected_component and selected_component.get('type') in pattern['components']:
            score += 25

        # Adjust based on app complexity
        if len(current_types) < 3 and missing <= 2:
            score += 20  # Good for simple apps
        elif len(current_types) > 8 and missing <= 1:
            score += 15  # Good for complex apps

        return max(score, 0)

    def _generate_combination_explanation(self, pattern, current_types):
        """Generate explanation for why this combination is recommended."""
        pattern_components = set(pattern['components'])
        current_components = set(current_types)

        missing = pattern_components - current_components
        existing = pattern_components.intersection(current_components)

        if missing:
            return f"Add {', '.join(missing)} to complete this {pattern['name'].lower()} pattern. You already have {', '.join(existing) if existing else 'the foundation'}."
        else:
            return f"Perfect! You have all components for a {pattern['name'].lower()}. Consider organizing them together."

    def _get_missing_components(self, pattern, current_types):
        """Get list of components missing from the pattern."""
        pattern_components = set(pattern['components'])
        current_components = set(current_types)
        return list(pattern_components - current_components)

    def _get_contextual_suggestions(self, selected_component, current_components):
        """Get suggestions based on the currently selected component."""
        suggestions = []
        comp_type = selected_component.get('type')

        # Component-specific suggestions
        contextual_patterns = {
            'button': ['form', 'card', 'modal'],
            'form': ['input', 'button', 'text'],
            'card': ['image', 'text', 'button'],
            'text': ['button', 'divider', 'image'],
            'image': ['text', 'button', 'card']
        }

        if comp_type in contextual_patterns:
            recommended = contextual_patterns[comp_type]
            current_types = [comp.get('type') for comp in current_components]

            for rec_type in recommended:
                if rec_type not in current_types:
                    suggestion = {
                        'id': f'contextual_{comp_type}_{rec_type}',
                        'name': f'{comp_type.title()} + {rec_type.title()}',
                        'description': f'Add {rec_type} to complement your {comp_type}',
                        'components': [comp_type, rec_type],
                        'score': 70,
                        'explanation': f'{rec_type.title()} works well with {comp_type} components',
                        'missing_components': [rec_type],
                        'use_cases': ['general']
                    }
                    suggestions.append(suggestion)

        return suggestions[:2]  # Limit contextual suggestions


class TutorialAIPlugin:
    def __init__(self, api_key=None):
        config = get_api_config()
        self.openai_api_key = api_key or config.openai_api_key
        self.ai_studio_api_key = config.ai_studio_api_key

        # Set OpenAI API key
        openai.api_key = self.openai_api_key
        self.context = {
            'current_step': 0,
            'completed_topics': set()
        }

        self.tutorials = {
            'getting_started': [
                "Welcome! Let's start with the basics. The main toolbar contains all your building blocks.",
                "Try dragging a component from the toolbar to the canvas.",
                "Great! Now you can customize the component using the properties panel on the right."
            ],
            'ai_features': [
                "Our AI features help you build faster. Try the 'Generate Suggestions' button.",
                "You can also generate images using AI - just describe what you want!",
                "The AI can help with layout suggestions and styling recommendations."
            ]
        }

    def generate_response(self, user_query):
        """Generate contextual tutorial responses based on user queries."""
        try:
            # Use the existing AI infrastructure
            prompt = f"""
            User Query: {user_query}
            Context: App Builder tutorial assistant
            Current Progress: {self.context['current_step']}
            Completed Topics: {', '.join(self.context['completed_topics'])}

            Provide a helpful, concise response that:
            1. Directly addresses the user's question
            2. Includes specific UI references when relevant
            3. Suggests next steps or related features
            """

            response = openai.Completion.create(
                engine="davinci",
                prompt=prompt,
                max_tokens=150,
                temperature=0.7
            )

            return response.choices[0].text.strip()
        except Exception as e:
            # Fallback to pre-defined responses if AI fails
            return self._get_fallback_response(user_query)

    def _get_fallback_response(self, query):
        """Provide pre-defined responses for common queries."""
        query = query.lower()
        if 'start' in query or 'begin' in query:
            return self.tutorials['getting_started'][0]
        elif 'ai' in query or 'generate' in query:
            return self.tutorials['ai_features'][0]
        return "What specific aspect of the App Builder would you like to learn about?"

    def update_progress(self, step_completed):
        """Update user's tutorial progress."""
        self.context['current_step'] = step_completed
        if 'user_progress' not in self.context:
            self.context['user_progress'] = {}
        self.context['user_progress'][step_completed] = True


