"""
Core application logic for App Builder 201.
This module contains the main classes and functions for building applications.
"""

import openai
from .config import get_api_config

class PluginManager:
    """
    Manages plugins for the application builder.
    """
    def __init__(self):
        self.plugins = {}

    def register_plugin(self, name, plugin):
        """
        Register a plugin with the manager.

        Args:
            name (str): The name of the plugin
            plugin (object): The plugin instance
        """
        self.plugins[name] = plugin

    def get_plugin(self, name):
        """
        Get a plugin by name.

        Args:
            name (str): The name of the plugin

        Returns:
            object: The plugin instance or None if not found
        """
        return self.plugins.get(name)


class AppBuilder:
    """
    Main class for building applications.
    """
    def __init__(self):
        self.components = []
        self.layouts = []
        self.styles = {}
        self.data = {}
        self.plugin_manager = PluginManager()

    def add_component(self, component_type, props={}):
        """
        Add a component to the application.

        Args:
            component_type (str): The type of component
            props (dict): The properties of the component
        """
        self.components.append({"type": component_type, "props": props})

    def add_layout(self, layout_type, components=[], styles={}):
        """
        Add a layout to the application.

        Args:
            layout_type (str): The type of layout
            components (list): The components in the layout
            styles (dict): The styles for the layout
        """
        self.layouts.append({"type": layout_type, "components": components, "styles": styles})

    def add_style(self, selector, style):
        """
        Add a style to the application.

        Args:
            selector (str): The CSS selector
            style (dict): The style properties
        """
        self.styles[selector] = style

    def add_data(self, key, value):
        """
        Add data to the application.

        Args:
            key (str): The data key
            value (any): The data value
        """
        self.data[key] = value

    def build(self):
        """
        Build the application structure.

        Returns:
            dict: The application structure
        """
        return {
            "components": [component for component in self.components],
            "layouts": [layout for layout in self.layouts],
            "styles": {selector: style for selector, style in self.styles.items()},
            "data": {key: value for key, value in self.data.items()},
        }

    def export(self, format='web', options=None):
        """
        Export the application in the specified format using enhanced code generator.

        Args:
            format (str): The export format ('web', 'mobile', 'react', 'vue', etc.)
            options (dict): Export options for enhanced generation

        Returns:
            str or dict: The exported application

        Raises:
            ValueError: If the format is invalid
        """
        # Import enhanced code generator
        try:
            from .enhanced_code_generator import EnhancedCodeGenerator, ExportOptions, ExportFormat, StyleFramework

            # Map legacy formats to new formats
            format_mapping = {
                'web': ExportFormat.REACT,
                'mobile': ExportFormat.REACT_NATIVE,
                'json': 'json'
            }

            if format == 'json':
                return self.build()

            # Use enhanced code generator for modern formats
            if format in format_mapping or hasattr(ExportFormat, format.upper().replace('-', '_')):
                generator = EnhancedCodeGenerator()

                # Create export options
                export_options = ExportOptions(
                    format=format_mapping.get(format, getattr(ExportFormat, format.upper().replace('-', '_'), ExportFormat.REACT)),
                    typescript=options.get('typescript', False) if options else False,
                    include_accessibility=options.get('include_accessibility', True) if options else True,
                    include_tests=options.get('include_tests', False) if options else False,
                    style_framework=getattr(StyleFramework, options.get('style_framework', 'STYLED_COMPONENTS').upper()) if options else StyleFramework.STYLED_COMPONENTS,
                    project_structure=options.get('project_structure', 'single-file') if options else 'single-file',
                    include_docker=options.get('include_docker', False) if options else False,
                    include_ci_cd=options.get('include_ci_cd', False) if options else False
                )

                return generator.generate_code(self.build(), export_options)

            # Fallback to legacy generation for backward compatibility
            elif format == 'web':
                return self._generate_web_code(self.build())
            elif format == 'mobile':
                return self._generate_mobile_code(self.build())
            else:
                raise ValueError(f"Invalid export format: {format}")

        except ImportError:
            # Fallback to legacy generation if enhanced generator is not available
            if format == 'web':
                return self._generate_web_code(self.build())
            elif format == 'mobile':
                return self._generate_mobile_code(self.build())
            elif format == 'json':
                return self.build()
            else:
                raise ValueError("Invalid export format")

    def _generate_web_code(self, app_structure):
        """
        Generate web code from the application structure.

        Args:
            app_structure (dict): The application structure

        Returns:
            str: The generated web code
        """
        # Implement HTML/React code generation
        html = "<!DOCTYPE html>\n<html>\n<head>\n  <title>Generated App</title>\n"

        # Add styles
        html += "  <style>\n"
        for selector, style in app_structure["styles"].items():
            html += f"    {selector} {{\n"
            for prop, value in style.items():
                html += f"      {prop}: {value};\n"
            html += "    }\n"
        html += "  </style>\n</head>\n<body>\n"

        # Add components
        html += "  <div id='app'>\n"
        for layout in app_structure["layouts"]:
            html += f"    <div class='{layout['type']}'>\n"
            for component in layout["components"]:
                component_data = next((c for c in app_structure["components"] if c["type"] == component), None)
                if component_data:
                    html += f"      <div class='{component_data['type']}'>\n"
                    for prop, value in component_data["props"].items():
                        if prop == "text":
                            html += f"        {value}\n"
                    html += "      </div>\n"
            html += "    </div>\n"
        html += "  </div>\n"

        # Add data
        html += "  <script>\n    const appData = " + str(app_structure["data"]) + ";\n  </script>\n"

        html += "</body>\n</html>"
        return html

    def _generate_mobile_code(self, app_structure):
        """
        Generate mobile code from the application structure.

        Args:
            app_structure (dict): The application structure

        Returns:
            str: The generated mobile code
        """
        # Implement React Native code generation
        code = "import React from 'react';\n"
        code += "import { View, Text, StyleSheet } from 'react-native';\n\n"

        # Generate component imports
        for component in app_structure["components"]:
            code += f"import {component['type']} from './components/{component['type']}';\n"

        code += "\nconst App = () => {\n"
        code += "  return (\n    <View style={styles.container}>\n"

        # Add layouts
        for layout in app_structure["layouts"]:
            code += f"      <View style={{{{styles.{layout['type']}}}}}>\n"
            for component in layout["components"]:
                component_data = next((c for c in app_structure["components"] if c["type"] == component), None)
                if component_data:
                    props = ", ".join([f"{k}: '{v}'" for k, v in component_data["props"].items()])
                    code += f"        <{component_data['type']} {{{{{props}}}}} />\n"
            code += "      </View>\n"

        code += "    </View>\n  );\n};\n\n"

        # Add styles
        code += "const styles = StyleSheet.create({\n"
        code += "  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n"
        for selector, style in app_structure["styles"].items():
            code += f"  {selector}: {{\n"
            for prop, value in style.items():
                code += f"    {prop}: '{value}',\n"
            code += "  },\n"
        code += "});\n\n"

        code += "export default App;"
        return code

    def register_plugin(self, plugin):
        """
        Register a plugin with the application builder.

        Args:
            plugin (object): The plugin instance
        """
        self.plugin_manager.register_plugin(plugin.__class__.__name__, plugin)

    def use_plugin(self, plugin_name, *args, **kwargs):
        """
        Use a plugin.

        Args:
            plugin_name (str): The name of the plugin
            *args: Arguments to pass to the plugin
            **kwargs: Keyword arguments to pass to the plugin

        Returns:
            any: The result of the plugin

        Raises:
            ValueError: If the plugin is not found
        """
        plugin = self.plugin_manager.get_plugin(plugin_name)
        if plugin:
            return plugin(*args, **kwargs)
        else:
            raise ValueError(f"Plugin '{plugin_name}' not found")


class AIPlugin:
    """
    Plugin for AI-powered features.
    """
    def __init__(self, api_key=None):
        config = get_api_config()
        self.openai_api_key = api_key or config.openai_api_key
        self.ai_studio_api_key = config.ai_studio_api_key
        self.stability_api_key = config.stability_api_key
        self.elevenlabs_api_key = config.elevenlabs_api_key

        # Set OpenAI API key
        openai.api_key = self.openai_api_key

    def get_suggestions(self, prompt):
        try:
            response = openai.Completion.create(
                engine="davinci",
                prompt=prompt,
                max_tokens=100
            )
            return response.choices[0].text.strip()
        except Exception as e:
            print(f"OpenAI API error: {str(e)}")
            return "Error generating suggestions"

    def generate_image(self, prompt, api='openai'):
        """
        Generate an image based on a prompt.

        Args:
            prompt (str): The prompt for the image generation
            api (str): The API to use ('openai', 'stability', or 'placeholder')

        Returns:
            str: The URL of the generated image
        """
        try:
            if api == 'openai' and self.openai_api_key:
                # Use OpenAI's DALL-E API
                response = openai.Image.create(
                    prompt=prompt,
                    n=1,
                    size="1024x1024"
                )
                return response['data'][0]['url']

            elif api == 'stability' and self.stability_api_key:
                # Use Stability AI's API
                # This is a placeholder for the actual implementation
                # In a real implementation, you would use the Stability AI SDK
                return f"https://via.placeholder.com/1024x1024?text=Stability+AI+Image:{prompt}"

            else:
                # Fallback to placeholder
                return f"https://via.placeholder.com/300x200?text=AI+Generated+Image:{prompt.replace(' ', '+')}"
        except Exception as e:
            print(f"Error generating image: {str(e)}")
            return f"https://via.placeholder.com/300x200?text=Error+Generating+Image"

    def __call__(self, *args, **kwargs):
        """
        Make the plugin callable.

        Args:
            *args: Arguments
            **kwargs: Keyword arguments

        Returns:
            any: The result of the plugin
        """
        method = kwargs.get('method', 'get_suggestions')
        if method == 'get_suggestions':
            return self.get_suggestions(kwargs.get('prompt', ''))
        elif method == 'generate_image':
            return self.generate_image(kwargs.get('prompt', ''))
        else:
            raise ValueError(f"Unknown method: {method}")


class TutorialAIPlugin:
    def __init__(self, api_key=None):
        config = get_api_config()
        self.openai_api_key = api_key or config.openai_api_key
        self.ai_studio_api_key = config.ai_studio_api_key

        # Set OpenAI API key
        openai.api_key = self.openai_api_key
        self.context = {
            'current_step': 0,
            'completed_topics': set()
        }

        self.tutorials = {
            'getting_started': [
                "Welcome! Let's start with the basics. The main toolbar contains all your building blocks.",
                "Try dragging a component from the toolbar to the canvas.",
                "Great! Now you can customize the component using the properties panel on the right."
            ],
            'ai_features': [
                "Our AI features help you build faster. Try the 'Generate Suggestions' button.",
                "You can also generate images using AI - just describe what you want!",
                "The AI can help with layout suggestions and styling recommendations."
            ]
        }

    def generate_response(self, user_query):
        """Generate contextual tutorial responses based on user queries."""
        try:
            # Use the existing AI infrastructure
            prompt = f"""
            User Query: {user_query}
            Context: App Builder tutorial assistant
            Current Progress: {self.context['current_step']}
            Completed Topics: {', '.join(self.context['completed_topics'])}

            Provide a helpful, concise response that:
            1. Directly addresses the user's question
            2. Includes specific UI references when relevant
            3. Suggests next steps or related features
            """

            response = openai.Completion.create(
                engine="davinci",
                prompt=prompt,
                max_tokens=150,
                temperature=0.7
            )

            return response.choices[0].text.strip()
        except Exception as e:
            # Fallback to pre-defined responses if AI fails
            return self._get_fallback_response(user_query)

    def _get_fallback_response(self, query):
        """Provide pre-defined responses for common queries."""
        query = query.lower()
        if 'start' in query or 'begin' in query:
            return self.tutorials['getting_started'][0]
        elif 'ai' in query or 'generate' in query:
            return self.tutorials['ai_features'][0]
        return "What specific aspect of the App Builder would you like to learn about?"

    def update_progress(self, step_completed):
        """Update user's tutorial progress."""
        self.context['current_step'] = step_completed
        if 'user_progress' not in self.context:
            self.context['user_progress'] = {}
        self.context['user_progress'][step_completed] = True


