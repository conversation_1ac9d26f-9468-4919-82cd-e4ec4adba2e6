from django.urls import re_path
from . import consumers
from websockets.consumers.collaboration import CollaborationConsumer

websocket_urlpatterns = [
    # Main application WebSocket endpoints
    re_path(r'^ws/app_builder/$', consumers.AppBuilderConsumer.as_asgi()),
    re_path(r'^ws/app_builder$', consumers.AppBuilderConsumer.as_asgi()),

    # App-specific endpoints with app_id parameter
    re_path(r'^ws/app/(?P<app_id>\d+)/$', consumers.AppBuilderConsumer.as_asgi()),
    re_path(r'^ws/app/(?P<app_id>\d+)$', consumers.AppBuilderConsumer.as_asgi()),

    # Collaboration endpoints
    re_path(r'^ws/collaboration/(?P<session_id>[^/]+)/$', CollaborationConsumer.as_asgi()),
    re_path(r'^ws/collaboration/(?P<session_id>[^/]+)$', CollaborationConsumer.as_asgi()),

    # Test and health check endpoints
    re_path(r'^ws/test/$', consumers.TestConsumer.as_asgi()),
    re_path(r'^ws/health/$', consumers.HealthCheckConsumer.as_asgi()),
    re_path(r'^ws/echo/$', consumers.SimpleEchoConsumer.as_asgi()),

    # Simple test endpoints
    re_path(r'^ws/simple/$', consumers.SimpleTestConsumer.as_asgi()),
    re_path(r'^ws/simple_echo/$', consumers.SimpleEchoConsumer.as_asgi()),

    # Root WebSocket endpoints (with and without trailing slash)
    re_path(r'^ws/$', consumers.SimpleEchoConsumer.as_asgi()),
    re_path(r'^ws$', consumers.SimpleEchoConsumer.as_asgi()),

    # Fallback for any other WebSocket paths
    re_path(r'^ws/.*$', consumers.SimpleEchoConsumer.as_asgi()),

    # Catch-all route for direct WebSocket connections without the ws/ prefix
    re_path(r'^.*$', consumers.SimpleEchoConsumer.as_asgi()),
]


